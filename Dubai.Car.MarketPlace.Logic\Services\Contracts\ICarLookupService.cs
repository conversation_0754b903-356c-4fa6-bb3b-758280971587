using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Service contract for car lookup operations (brands, body types, fuel types)
    /// </summary>
    public interface ICarLookupService
    {
        /// <summary>
        /// Gets all brands
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive brands</param>
        /// <returns>List of brand responses</returns>
        Task<ApiResponse<List<BrandResponseDto>>> GetBrandsAsync(bool includeInactive = false);

        /// <summary>
        /// Gets a brand by ID
        /// </summary>
        /// <param name="brandId">The ID of the brand</param>
        /// <returns>The brand response</returns>
        Task<ApiResponse<BrandResponseDto>> GetBrandByIdAsync(Guid brandId);

        /// <summary>
        /// Creates a new brand
        /// </summary>
        /// <param name="name">The brand name</param>
        /// <param name="description">The brand description</param>
        /// <param name="countryOfOrigin">The country of origin</param>
        /// <param name="logoImageKey">The logo image key</param>
        /// <returns>The created brand response</returns>
        Task<ApiResponse<BrandResponseDto>> CreateBrandAsync(string name, string? description = null, string? countryOfOrigin = null, string? logoImageKey = null);

        /// <summary>
        /// Updates an existing brand
        /// </summary>
        /// <param name="brandId">The ID of the brand to update</param>
        /// <param name="name">The brand name</param>
        /// <param name="description">The brand description</param>
        /// <param name="countryOfOrigin">The country of origin</param>
        /// <param name="logoImageKey">The logo image key</param>
        /// <param name="isActive">Whether the brand is active</param>
        /// <returns>The updated brand response</returns>
        Task<ApiResponse<BrandResponseDto>> UpdateBrandAsync(Guid brandId, string? name = null, string? description = null, string? countryOfOrigin = null, string? logoImageKey = null, bool? isActive = null);

        /// <summary>
        /// Deletes a brand (soft delete)
        /// </summary>
        /// <param name="brandId">The ID of the brand to delete</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> DeleteBrandAsync(Guid brandId);

        /// <summary>
        /// Gets all body types
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive body types</param>
        /// <returns>List of body type responses</returns>
        Task<ApiResponse<List<BodyTypeResponseDto>>> GetBodyTypesAsync(bool includeInactive = false);

        /// <summary>
        /// Gets a body type by ID
        /// </summary>
        /// <param name="bodyTypeId">The ID of the body type</param>
        /// <returns>The body type response</returns>
        Task<ApiResponse<BodyTypeResponseDto>> GetBodyTypeByIdAsync(Guid bodyTypeId);

        /// <summary>
        /// Creates a new body type
        /// </summary>
        /// <param name="name">The body type name</param>
        /// <param name="description">The body type description</param>
        /// <param name="iconImageKey">The icon image key</param>
        /// <returns>The created body type response</returns>
        Task<ApiResponse<BodyTypeResponseDto>> CreateBodyTypeAsync(string name, string? description = null, string? iconImageKey = null);

        /// <summary>
        /// Updates an existing body type
        /// </summary>
        /// <param name="bodyTypeId">The ID of the body type to update</param>
        /// <param name="name">The body type name</param>
        /// <param name="description">The body type description</param>
        /// <param name="iconImageKey">The icon image key</param>
        /// <param name="isActive">Whether the body type is active</param>
        /// <returns>The updated body type response</returns>
        Task<ApiResponse<BodyTypeResponseDto>> UpdateBodyTypeAsync(Guid bodyTypeId, string? name = null, string? description = null, string? iconImageKey = null, bool? isActive = null);

        /// <summary>
        /// Deletes a body type (soft delete)
        /// </summary>
        /// <param name="bodyTypeId">The ID of the body type to delete</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> DeleteBodyTypeAsync(Guid bodyTypeId);

        /// <summary>
        /// Gets all fuel types
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive fuel types</param>
        /// <returns>List of fuel type responses</returns>
        Task<ApiResponse<List<FuelTypeResponseDto>>> GetFuelTypesAsync(bool includeInactive = false);

        /// <summary>
        /// Gets a fuel type by ID
        /// </summary>
        /// <param name="fuelTypeId">The ID of the fuel type</param>
        /// <returns>The fuel type response</returns>
        Task<ApiResponse<FuelTypeResponseDto>> GetFuelTypeByIdAsync(Guid fuelTypeId);

        /// <summary>
        /// Creates a new fuel type
        /// </summary>
        /// <param name="name">The fuel type name</param>
        /// <param name="description">The fuel type description</param>
        /// <param name="iconImageKey">The icon image key</param>
        /// <param name="environmentalRating">The environmental rating</param>
        /// <returns>The created fuel type response</returns>
        Task<ApiResponse<FuelTypeResponseDto>> CreateFuelTypeAsync(string name, string? description = null, string? iconImageKey = null, int? environmentalRating = null);

        /// <summary>
        /// Updates an existing fuel type
        /// </summary>
        /// <param name="fuelTypeId">The ID of the fuel type to update</param>
        /// <param name="name">The fuel type name</param>
        /// <param name="description">The fuel type description</param>
        /// <param name="iconImageKey">The icon image key</param>
        /// <param name="environmentalRating">The environmental rating</param>
        /// <param name="isActive">Whether the fuel type is active</param>
        /// <returns>The updated fuel type response</returns>
        Task<ApiResponse<FuelTypeResponseDto>> UpdateFuelTypeAsync(Guid fuelTypeId, string? name = null, string? description = null, string? iconImageKey = null, int? environmentalRating = null, bool? isActive = null);

        /// <summary>
        /// Deletes a fuel type (soft delete)
        /// </summary>
        /// <param name="fuelTypeId">The ID of the fuel type to delete</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> DeleteFuelTypeAsync(Guid fuelTypeId);
    }
}
