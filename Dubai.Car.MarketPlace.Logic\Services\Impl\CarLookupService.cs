using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Threading.Tasks;
using Ubiety.Dns.Core;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Service implementation for car lookup operations
    /// </summary>
    public class CarLookupService : ICarLookupService
    {
        private readonly CarContext _context;
        private readonly ILogger<CarLookupService> _logger;
        private readonly IAwsService _awsService;

        public CarLookupService(CarContext context, ILogger<CarLookupService> logger, IAwsService awsService)
        {
            _context = context;
            _logger = logger;
            _awsService = awsService;
        }

        #region Brand Operations

        public async Task<ApiResponse<List<BrandResponseDto>>> GetBrandsAsync(bool includeInactive = false)
        {
            try
            {
                var query = _context.Brands.Where(b => !b.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(b => b.IsActive);
                }

                var brands = await query
                    .OrderBy(b => b.DisplayOrder)
                    .ThenBy(b => b.Name)
                    .ToListAsync();

                var brandDtos = brands.Select(MapToBrandResponseDtoAsync).ToList();
                return ApiResponse<List<BrandResponseDto>>.Success(brandDtos, ResponseMessages.SuccessResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting brands");
                return ApiResponse<List<BrandResponseDto>>.Failed("An error occurred while retrieving brands", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<BrandResponseDto>> GetBrandByIdAsync(Guid brandId)
        {
            try
            {
                var brand = await _context.Brands
                    .Where(b => b.Id == brandId && !b.IsDeleted)
                    .FirstOrDefaultAsync();

                if (brand == null)
                {
                    return ApiResponse<BrandResponseDto>.Failed("Brand not found", HttpStatusCode.NotFound);
                }

                var brandDto = MapToBrandResponseDtoAsync(brand);
                return ApiResponse<BrandResponseDto>.Success(brandDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting brand {BrandId}", brandId);
                return ApiResponse<BrandResponseDto>.Failed("An error occurred while retrieving the brand", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<BrandResponseDto>> CreateBrandAsync(string name, string? description = null, string? countryOfOrigin = null, string? logoImageKey = null)
        {
            try
            {
                // Check if brand with same name already exists
                var existingBrand = await _context.Brands
                    .Where(b => b.Name.ToLower() == name.ToLower() && !b.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingBrand != null)
                {
                    return ApiResponse<BrandResponseDto>.Failed("A brand with this name already exists", HttpStatusCode.Conflict);
                }

                var brand = new Brand
                {
                    Name = name,
                    Description = description,
                    CountryOfOrigin = countryOfOrigin,
                    LogoImageKey = logoImageKey,
                    IsActive = true,
                    DisplayOrder = await GetNextDisplayOrderAsync<Brand>()
                };

                _context.Brands.Add(brand);
                await _context.SaveChangesAsync();

                var brandDto = MapToBrandResponseDtoAsync(brand);
                return ApiResponse<BrandResponseDto>.Success(brandDto, "Brand created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating brand {Name}", name);
                return ApiResponse<BrandResponseDto>.Failed("An error occurred while creating the brand", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<BrandResponseDto>> UpdateBrandAsync(Guid brandId, string? name = null, string? description = null, string? countryOfOrigin = null, string? logoImageKey = null, bool? isActive = null)
        {
            try
            {
                var brand = await _context.Brands
                    .Where(b => b.Id == brandId && !b.IsDeleted)
                    .FirstOrDefaultAsync();

                if (brand == null)
                {
                    return ApiResponse<BrandResponseDto>.Failed("Brand not found", HttpStatusCode.NotFound);
                }

                // Check if name is being changed and if it conflicts with existing brand
                if (!string.IsNullOrEmpty(name) && name.ToLower() != brand.Name.ToLower())
                {
                    var existingBrand = await _context.Brands
                        .Where(b => b.Name.ToLower() == name.ToLower() && !b.IsDeleted && b.Id != brandId)
                        .FirstOrDefaultAsync();

                    if (existingBrand != null)
                    {
                        return ApiResponse<BrandResponseDto>.Failed("A brand with this name already exists", HttpStatusCode.Conflict);
                    }

                    brand.Name = name;
                }

                if (description != null) brand.Description = description;
                if (countryOfOrigin != null) brand.CountryOfOrigin = countryOfOrigin;
                if (logoImageKey != null) brand.LogoImageKey = logoImageKey;
                if (isActive.HasValue) brand.IsActive = isActive.Value;

                await _context.SaveChangesAsync();

                var brandDto = MapToBrandResponseDtoAsync(brand);
                return ApiResponse<BrandResponseDto>.Success(brandDto, "Brand updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating brand {BrandId}", brandId);
                return ApiResponse<BrandResponseDto>.Failed("An error occurred while updating the brand", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> DeleteBrandAsync(Guid brandId)
        {
            try
            {
                var brand = await _context.Brands
                    .Where(b => b.Id == brandId && !b.IsDeleted)
                    .FirstOrDefaultAsync();

                if (brand == null)
                {
                    return ApiResponse<bool>.Failed("Brand not found", HttpStatusCode.NotFound);
                }

                // Check if brand is being used by any cars
                var carsCount = await _context.Cars
                    .Where(c => c.BrandId == brandId && !c.IsDeleted)
                    .CountAsync();

                if (carsCount > 0)
                {
                    return ApiResponse<bool>.Failed($"Cannot delete brand. It is being used by {carsCount} car(s)", HttpStatusCode.Conflict);
                }

                brand.IsDeleted = true;
                brand.DeletedOn = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Brand deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting brand {BrandId}", brandId);
                return ApiResponse<bool>.Failed("An error occurred while deleting the brand", HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        #region Body Type Operations

        public async Task<ApiResponse<List<BodyTypeResponseDto>>> GetBodyTypesAsync(bool includeInactive = false)
        {
            try
            {
                var query = _context.BodyTypes.Where(bt => !bt.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(bt => bt.IsActive);
                }

                var bodyTypes = await query
                    .OrderBy(bt => bt.DisplayOrder)
                    .ThenBy(bt => bt.Name)
                    .ToListAsync();

                var bodyTypeDtos = bodyTypes.Select(MapToBodyTypeResponseDto).ToList();

                return ApiResponse<List<BodyTypeResponseDto>>.Success(bodyTypeDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting body types");
                return ApiResponse<List<BodyTypeResponseDto>>.Failed("An error occurred while retrieving body types", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<BodyTypeResponseDto>> GetBodyTypeByIdAsync(Guid bodyTypeId)
        {
            try
            {
                var bodyType = await _context.BodyTypes
                    .Where(bt => bt.Id == bodyTypeId && !bt.IsDeleted)
                    .FirstOrDefaultAsync();

                if (bodyType == null)
                {
                    return ApiResponse<BodyTypeResponseDto>.Failed("Body type not found", HttpStatusCode.NotFound);
                }

                var bodyTypeDto = MapToBodyTypeResponseDto(bodyType);
                return ApiResponse<BodyTypeResponseDto>.Success(bodyTypeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting body type {BodyTypeId}", bodyTypeId);
                return ApiResponse<BodyTypeResponseDto>.Failed("An error occurred while retrieving the body type", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<BodyTypeResponseDto>> CreateBodyTypeAsync(string name, string? description = null, string? iconImageKey = null)
        {
            try
            {
                // Check if body type with same name already exists
                var existingBodyType = await _context.BodyTypes
                    .Where(bt => bt.Name.ToLower() == name.ToLower() && !bt.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingBodyType != null)
                {
                    return ApiResponse<BodyTypeResponseDto>.Failed("A body type with this name already exists", HttpStatusCode.Conflict);
                }

                var bodyType = new BodyType
                {
                    Name = name,
                    Description = description,
                    IconImageKey = iconImageKey,
                    IsActive = true,
                    DisplayOrder = await GetNextDisplayOrderAsync<BodyType>()
                };

                _context.BodyTypes.Add(bodyType);
                await _context.SaveChangesAsync();

                var bodyTypeDto = MapToBodyTypeResponseDto(bodyType);
                return ApiResponse<BodyTypeResponseDto>.Success(bodyTypeDto, "Body type created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating body type {Name}", name);
                return ApiResponse<BodyTypeResponseDto>.Failed("An error occurred while creating the body type", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<BodyTypeResponseDto>> UpdateBodyTypeAsync(Guid bodyTypeId, string? name = null, string? description = null, string? iconImageKey = null, bool? isActive = null)
        {
            try
            {
                var bodyType = await _context.BodyTypes
                    .Where(bt => bt.Id == bodyTypeId && !bt.IsDeleted)
                    .FirstOrDefaultAsync();

                if (bodyType == null)
                {
                    return ApiResponse<BodyTypeResponseDto>.Failed("Body type not found", HttpStatusCode.NotFound);
                }

                // Check if name is being changed and if it conflicts with existing body type
                if (!string.IsNullOrEmpty(name) && name.ToLower() != bodyType.Name.ToLower())
                {
                    var existingBodyType = await _context.BodyTypes
                        .Where(bt => bt.Name.ToLower() == name.ToLower() && !bt.IsDeleted && bt.Id != bodyTypeId)
                        .FirstOrDefaultAsync();

                    if (existingBodyType != null)
                    {
                        return ApiResponse<BodyTypeResponseDto>.Failed("A body type with this name already exists", HttpStatusCode.Conflict);
                    }

                    bodyType.Name = name;
                }

                if (description != null) bodyType.Description = description;
                if (iconImageKey != null) bodyType.IconImageKey = iconImageKey;
                if (isActive.HasValue) bodyType.IsActive = isActive.Value;

                await _context.SaveChangesAsync();

                var bodyTypeDto = MapToBodyTypeResponseDto(bodyType);
                return ApiResponse<BodyTypeResponseDto>.Success(bodyTypeDto, "Body type updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating body type {BodyTypeId}", bodyTypeId);
                return ApiResponse<BodyTypeResponseDto>.Failed("An error occurred while updating the body type", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> DeleteBodyTypeAsync(Guid bodyTypeId)
        {
            try
            {
                var bodyType = await _context.BodyTypes
                    .Where(bt => bt.Id == bodyTypeId && !bt.IsDeleted)
                    .FirstOrDefaultAsync();

                if (bodyType == null)
                {
                    return ApiResponse<bool>.Failed("Body type not found", HttpStatusCode.NotFound);
                }

                // Check if body type is being used by any cars
                var carsCount = await _context.Cars
                    .Where(c => c.BodyTypeId == bodyTypeId && !c.IsDeleted)
                    .CountAsync();

                if (carsCount > 0)
                {
                    return ApiResponse<bool>.Failed($"Cannot delete body type. It is being used by {carsCount} car(s)", HttpStatusCode.Conflict);
                }

                bodyType.IsDeleted = true;
                bodyType.DeletedOn = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Body type deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting body type {BodyTypeId}", bodyTypeId);
                return ApiResponse<bool>.Failed("An error occurred while deleting the body type", HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        #region Fuel Type Operations

        public async Task<ApiResponse<List<FuelTypeResponseDto>>> GetFuelTypesAsync(bool includeInactive = false)
        {
            try
            {
                var query = _context.FuelTypes.Where(ft => !ft.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(ft => ft.IsActive);
                }

                var fuelTypes = await query
                    .OrderBy(ft => ft.DisplayOrder)
                    .ThenBy(ft => ft.Name)
                    .ToListAsync();

                var fuelTypeDtos = fuelTypes.Select(MapToFuelTypeResponseDto).ToList();

                return ApiResponse<List<FuelTypeResponseDto>>.Success(fuelTypeDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting fuel types");
                return ApiResponse<List<FuelTypeResponseDto>>.Failed("An error occurred while retrieving fuel types", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<FuelTypeResponseDto>> GetFuelTypeByIdAsync(Guid fuelTypeId)
        {
            try
            {
                var fuelType = await _context.FuelTypes
                    .Where(ft => ft.Id == fuelTypeId && !ft.IsDeleted)
                    .FirstOrDefaultAsync();

                if (fuelType == null)
                {
                    return ApiResponse<FuelTypeResponseDto>.Failed("Fuel type not found", HttpStatusCode.NotFound);
                }

                var fuelTypeDto = MapToFuelTypeResponseDto(fuelType);
                return ApiResponse<FuelTypeResponseDto>.Success(fuelTypeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting fuel type {FuelTypeId}", fuelTypeId);
                return ApiResponse<FuelTypeResponseDto>.Failed("An error occurred while retrieving the fuel type", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<FuelTypeResponseDto>> CreateFuelTypeAsync(string name, string? description = null, string? iconImageKey = null, int? environmentalRating = null)
        {
            try
            {
                // Check if fuel type with same name already exists
                var existingFuelType = await _context.FuelTypes
                    .Where(ft => ft.Name.ToLower() == name.ToLower() && !ft.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingFuelType != null)
                {
                    return ApiResponse<FuelTypeResponseDto>.Failed("A fuel type with this name already exists", HttpStatusCode.Conflict);
                }

                var fuelType = new FuelType
                {
                    Name = name,
                    Description = description,
                    IconImageKey = iconImageKey,
                    EnvironmentalRating = environmentalRating,
                    IsActive = true,
                    DisplayOrder = await GetNextDisplayOrderAsync<FuelType>()
                };

                _context.FuelTypes.Add(fuelType);
                await _context.SaveChangesAsync();

                var fuelTypeDto = MapToFuelTypeResponseDto(fuelType);
                return ApiResponse<FuelTypeResponseDto>.Success(fuelTypeDto, "Fuel type created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating fuel type {Name}", name);
                return ApiResponse<FuelTypeResponseDto>.Failed("An error occurred while creating the fuel type", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<FuelTypeResponseDto>> UpdateFuelTypeAsync(Guid fuelTypeId, string? name = null, string? description = null, string? iconImageKey = null, int? environmentalRating = null, bool? isActive = null)
        {
            try
            {
                var fuelType = await _context.FuelTypes
                    .Where(ft => ft.Id == fuelTypeId && !ft.IsDeleted)
                    .FirstOrDefaultAsync();

                if (fuelType == null)
                {
                    return ApiResponse<FuelTypeResponseDto>.Failed("Fuel type not found", HttpStatusCode.NotFound);
                }

                // Check if name is being changed and if it conflicts with existing fuel type
                if (!string.IsNullOrEmpty(name) && name.ToLower() != fuelType.Name.ToLower())
                {
                    var existingFuelType = await _context.FuelTypes
                        .Where(ft => ft.Name.ToLower() == name.ToLower() && !ft.IsDeleted && ft.Id != fuelTypeId)
                        .FirstOrDefaultAsync();

                    if (existingFuelType != null)
                    {
                        return ApiResponse<FuelTypeResponseDto>.Failed("A fuel type with this name already exists", HttpStatusCode.Conflict);
                    }

                    fuelType.Name = name;
                }

                if (description != null) fuelType.Description = description;
                if (iconImageKey != null) fuelType.IconImageKey = iconImageKey;
                if (environmentalRating.HasValue) fuelType.EnvironmentalRating = environmentalRating.Value;
                if (isActive.HasValue) fuelType.IsActive = isActive.Value;

                await _context.SaveChangesAsync();

                var fuelTypeDto = MapToFuelTypeResponseDto(fuelType);
                return ApiResponse<FuelTypeResponseDto>.Success(fuelTypeDto, "Fuel type updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating fuel type {FuelTypeId}", fuelTypeId);
                return ApiResponse<FuelTypeResponseDto>.Failed("An error occurred while updating the fuel type", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> DeleteFuelTypeAsync(Guid fuelTypeId)
        {
            try
            {
                var fuelType = await _context.FuelTypes
                    .Where(ft => ft.Id == fuelTypeId && !ft.IsDeleted)
                    .FirstOrDefaultAsync();

                if (fuelType == null)
                {
                    return ApiResponse<bool>.Failed("Fuel type not found", HttpStatusCode.NotFound);
                }

                // Check if fuel type is being used by any cars
                var carsCount = await _context.Cars
                    .Where(c => c.FuelTypeId == fuelTypeId && !c.IsDeleted)
                    .CountAsync();

                if (carsCount > 0)
                {
                    return ApiResponse<bool>.Failed($"Cannot delete fuel type. It is being used by {carsCount} car(s)", HttpStatusCode.Conflict);
                }

                fuelType.IsDeleted = true;
                fuelType.DeletedOn = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Fuel type deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting fuel type {FuelTypeId}", fuelTypeId);
                return ApiResponse<bool>.Failed("An error occurred while deleting the fuel type", HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        #region Helper Methods

        private async Task<int> GetNextDisplayOrderAsync<T>() where T : BaseEntity
        {
            int maxOrder = 0;

            if (typeof(T) == typeof(Brand))
            {
                maxOrder = await _context.Brands.Where(b => !b.IsDeleted).MaxAsync(b => (int?)b.DisplayOrder) ?? 0;
            }
            else if (typeof(T) == typeof(BodyType))
            {
                maxOrder = await _context.BodyTypes.Where(bt => !bt.IsDeleted).MaxAsync(bt => (int?)bt.DisplayOrder) ?? 0;
            }
            else if (typeof(T) == typeof(FuelType))
            {
                maxOrder = await _context.FuelTypes.Where(ft => !ft.IsDeleted).MaxAsync(ft => (int?)ft.DisplayOrder) ?? 0;
            }

            return maxOrder + 1;
        }

        private BrandResponseDto MapToBrandResponseDtoAsync(Brand brand)
        {
            return new BrandResponseDto
            {
                Id = brand.Id,
                Name = brand.Name,
                Description = brand.Description,
                LogoImageUrl = !string.IsNullOrEmpty(brand.LogoImageKey) ? _awsService.GetPresignedUrlAsync(brand.LogoImageKey).Result : null,
                CountryOfOrigin = brand.CountryOfOrigin,
                IsActive = brand.IsActive,
                DisplayOrder = brand.DisplayOrder
            };
        }

        private BodyTypeResponseDto MapToBodyTypeResponseDto(BodyType bodyType)
        {
            return new BodyTypeResponseDto
            {
                Id = bodyType.Id,
                Name = bodyType.Name,
                Description = bodyType.Description,
                IconImageUrl = !string.IsNullOrEmpty(bodyType.IconImageKey) ? _awsService.GetPresignedUrlAsync(bodyType.IconImageKey).Result : null,
                IsActive = bodyType.IsActive,
                DisplayOrder = bodyType.DisplayOrder
            };
        }

        private FuelTypeResponseDto MapToFuelTypeResponseDto(FuelType fuelType)
        {
            return new FuelTypeResponseDto
            {
                Id = fuelType.Id,
                Name = fuelType.Name,
                Description = fuelType.Description,
                IconImageUrl = !string.IsNullOrEmpty(fuelType.IconImageKey) ? _awsService.GetPresignedUrlAsync(fuelType.IconImageKey).Result : null,
                IsActive = fuelType.IsActive,
                DisplayOrder = fuelType.DisplayOrder,
                EnvironmentalRating = fuelType.EnvironmentalRating
            };
        }

        #endregion
    }
}
