﻿using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Dubai.Car.MarketPlace.Data.Database
{
    public class CarContext : IdentityDbContext<User, Role, Guid>
    {
        public CarContext(DbContextOptions<CarContext> options)
            : base(options)
        {
        }        // Db Sets for your entities can be defined here

        public DbSet<BlackListedToken> BlackListedTokens { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<Vendor> Vendors { get; set; }

        // Car Management Entities
        public DbSet<Brand> Brands { get; set; }
        public DbSet<BodyType> BodyTypes { get; set; }
        public DbSet<FuelType> FuelTypes { get; set; }
        public DbSet<Entities.Car> Cars { get; set; }
        public DbSet<CarSpecification> CarSpecifications { get; set; }
        public DbSet<CarSpecificationValue> CarSpecificationValues { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            // Additional model configurations can go here

            // rename IdentityUserRole to UserRole to avoid conflicts with IdentityDbContext
            modelBuilder.Entity<User>().ToTable("Users");
            modelBuilder.Entity<Role>().ToTable("Roles");
            modelBuilder.Entity<IdentityUserRole<Guid>>().ToTable("UserRoles");
            modelBuilder.Entity<IdentityUserClaim<Guid>>().ToTable("UserClaims");
            modelBuilder.Entity<IdentityUserLogin<Guid>>().ToTable("UserLogins");
            modelBuilder.Entity<IdentityRoleClaim<Guid>>().ToTable("RoleClaims");

            // Configure composite primary key for UserPermission
            modelBuilder.Entity<UserPermission>()
                .HasKey(up => new { up.UserId, up.Permission });

            // Configure Car Management entities
            ConfigureCarEntities(modelBuilder);

        }

        public override int SaveChanges()
        {
            ApplyEntityTracking();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ApplyEntityTracking();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void ApplyEntityTracking()
        {
            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.Entity is BaseEntity baseEntity)
                {
                    var now = DateTime.UtcNow;

                    switch (entry.State)
                    {
                        case EntityState.Added:
                            baseEntity.CreatedOn = now;
                            baseEntity.ModifiedOn = null;
                            baseEntity.IsDeleted = false;
                            break;

                        case EntityState.Modified:
                            baseEntity.ModifiedOn = now;
                            break;

                        case EntityState.Deleted:
                            // Soft delete handling
                            entry.State = EntityState.Modified;
                            baseEntity.IsDeleted = true;
                            baseEntity.DeletedOn = now;
                            break;
                    }
                }
            }
        }

        private void ConfigureCarEntities(ModelBuilder modelBuilder)
        {
            // Brand configuration
            modelBuilder.Entity<Brand>()
                .HasIndex(b => b.Name)
                .IsUnique();

            // BodyType configuration
            modelBuilder.Entity<BodyType>()
                .HasIndex(bt => bt.Name)
                .IsUnique();

            // FuelType configuration
            modelBuilder.Entity<FuelType>()
                .HasIndex(ft => ft.Name)
                .IsUnique();

            // CarSpecification configuration
            modelBuilder.Entity<CarSpecification>()
                .HasIndex(cs => cs.Name)
                .IsUnique();

            // Vendor configuration
            modelBuilder.Entity<Vendor>()
                .HasIndex(v => v.TradeLicenseNumber)
                .IsUnique();

            modelBuilder.Entity<Vendor>()
                .Property(x => x.Id)
                .ValueGeneratedNever();

            modelBuilder.Entity<Vendor>()
                .HasIndex(v => v.BusinessEmail)
                .IsUnique();

            modelBuilder.Entity<Vendor>()
                .HasOne(v => v.User)
                .WithMany()
                .HasForeignKey(v => v.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Car configuration
            modelBuilder.Entity<Entities.Car>()
                .HasOne(c => c.Brand)
                .WithMany(b => b.Cars)
                .HasForeignKey(c => c.BrandId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Entities.Car>()
                .HasOne(c => c.BodyType)
                .WithMany(bt => bt.Cars)
                .HasForeignKey(c => c.BodyTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Entities.Car>()
                .HasOne(c => c.FuelType)
                .WithMany(ft => ft.Cars)
                .HasForeignKey(c => c.FuelTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Entities.Car>()
                .HasOne(c => c.Vendor)
                .WithMany(v => v.Cars)
                .HasForeignKey(c => c.VendorId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Entities.Car>()
                .HasOne(c => c.User)
                .WithMany()
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // CarSpecificationValue configuration
            modelBuilder.Entity<CarSpecificationValue>()
                .HasOne(csv => csv.Car)
                .WithMany(c => c.SpecificationValues)
                .HasForeignKey(csv => csv.CarId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<CarSpecificationValue>()
                .HasOne(csv => csv.CarSpecification)
                .WithMany(cs => cs.CarSpecificationValues)
                .HasForeignKey(csv => csv.CarSpecificationId)
                .OnDelete(DeleteBehavior.Restrict);

            // Composite unique index for CarSpecificationValue
            modelBuilder.Entity<CarSpecificationValue>()
                .HasIndex(csv => new { csv.CarId, csv.CarSpecificationId })
                .IsUnique();
        }
    }
}
