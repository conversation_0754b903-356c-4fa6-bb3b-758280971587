﻿using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Common.Constants;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WatchDog;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Aws Services
    /// </summary>
    public class AwsService : IAwsService
    {
        private readonly AwsSettings _awsSettings;
        private readonly ILogger<AwsService> _logger;
        private readonly IAmazonS3 _s3Client;

        /// <summary>
        ///
        /// </summary>
        /// <param name="awsSettings"></param>
        /// <param name="logger"></param>
        public AwsService(
            IOptions<AwsSettings> awsSettings,
            ILogger<AwsService> logger)
        {
            _awsSettings = awsSettings.Value;
            _logger = logger;

            // Initialize S3 client
            _s3Client = new AmazonS3Client(
                _awsSettings.AccessKey,
                _awsSettings.SecretKey,
                RegionEndpoint.GetBySystemName(_awsSettings.Region));
        }

        #region Upload Methods
        /// <summary>
        /// Uploads a file to S3, automatically choosing between regular and multipart upload based on file size
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="folderName">The folder name in S3</param>
        /// <returns>The S3 key of the uploaded file</returns>
        /// <exception cref="ArgumentException">Thrown when file is invalid or too large</exception>
        public async Task<string> UploadFileAsync(IFormFile file, string folderName)
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("File is empty or null");
            }

            // Determine max file size based on file type
            int maxFileSizeMB = DetermineMaxFileSize(file.ContentType, folderName);

            // Check file size
            long maxFileSizeBytes = maxFileSizeMB * 1024 * 1024; // Convert MB to bytes
            if (file.Length > maxFileSizeBytes)
            {
                _logger.LogWarning("File size exceeds the maximum allowed size of {MaxSize}MB: {FileName}, {FileSize}bytes",
                    maxFileSizeMB, file.FileName, file.Length);
                WatchLogger.LogWarning($"File size exceeds the maximum allowed size of {maxFileSizeMB}MB: {file.FileName}, {file.Length}bytes",
                    "UploadFileAsync");
                throw new ArgumentException($"File size exceeds the maximum allowed size of {maxFileSizeMB}MB");
            }

            // Determine upload method based on file size
            long multipartThresholdBytes = AwsConstants.Upload.MultipartThresholdMB * 1024 * 1024;

            if (file.Length > multipartThresholdBytes)
            {
                _logger.LogInformation("Using multipart upload for large file: {FileName}, Size: {FileSize}MB",
                    file.FileName, file.Length / (1024 * 1024));
                WatchLogger.Log($"Using multipart upload for large file: {file.FileName}, Size: {file.Length / (1024 * 1024)}MB",
                    "UploadFileAsync");

                return await UploadLargeFileAsync(file, folderName);
            }
            else
            {
                _logger.LogInformation("Using regular upload for file: {FileName}, Size: {FileSize}MB",
                    file.FileName, file.Length / (1024 * 1024));

                return await UploadRegularFileAsync(file, folderName);
            }
        }
        #endregion

        #region Multipart Upload Methods - For Large Files
        /// <summary>
        /// Uploads a large file using multipart upload with progress tracking
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="folderName">The folder name in S3</param>
        /// <param name="progress">Optional progress reporter</param>
        /// <returns>The S3 key of the uploaded file</returns>
        public async Task<string> UploadLargeFileAsync(IFormFile file, string folderName, IProgress<int>? progress = null)
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("File is empty or null");
            }

            // Generate a unique key for the file
            string key = GenerateS3Key(folderName, file.FileName);

            _logger.LogInformation("Starting multipart upload for file: {FileName}, Size: {FileSize}MB",
                file.FileName, file.Length / (1024 * 1024));
            WatchLogger.Log($"Starting multipart upload for file: {file.FileName}, Size: {file.Length / (1024 * 1024)}MB",
                "UploadLargeFileAsync");

            // Initialize multipart upload
            var initiateRequest = new InitiateMultipartUploadRequest
            {
                BucketName = _awsSettings.BucketName,
                Key = key,
                ContentType = file.ContentType
            };

            var initiateResponse = await _s3Client.InitiateMultipartUploadAsync(initiateRequest);
            var uploadId = initiateResponse.UploadId;

            var completedParts = new List<PartETag>();
            var partSize = AwsConstants.Upload.MultipartPartSizeMB * 1024 * 1024; // Convert MB to bytes
            var totalParts = (int)Math.Ceiling((double)file.Length / partSize);

            try
            {
                using var fileStream = file.OpenReadStream();
                var semaphore = new SemaphoreSlim(AwsConstants.Upload.MaxConcurrentParts);
                var uploadTasks = new List<Task>();

                for (int partNumber = 1; partNumber <= totalParts; partNumber++)
                {
                    var currentPartNumber = partNumber;
                    var currentPartSize = Math.Min(partSize, file.Length - (partNumber - 1) * partSize);

                    var uploadTask = Task.Run(async () =>
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            await UploadPartAsync(fileStream, uploadId, key, currentPartNumber, currentPartSize, completedParts);

                            // Report progress
                            var progressPercentage = (int)((double)currentPartNumber / totalParts * 100);
                            progress?.Report(progressPercentage);

                            _logger.LogDebug("Completed part {PartNumber}/{TotalParts} for file {FileName}",
                                currentPartNumber, totalParts, file.FileName);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    });

                    uploadTasks.Add(uploadTask);
                }

                // Wait for all parts to complete
                await Task.WhenAll(uploadTasks);

                // Complete the multipart upload
                var completeRequest = new CompleteMultipartUploadRequest
                {
                    BucketName = _awsSettings.BucketName,
                    Key = key,
                    UploadId = uploadId,
                    PartETags = completedParts.OrderBy(p => p.PartNumber).ToList()
                };

                await _s3Client.CompleteMultipartUploadAsync(completeRequest);

                _logger.LogInformation("Successfully completed multipart upload for file: {FileName}", file.FileName);
                WatchLogger.Log($"Successfully completed multipart upload for file: {file.FileName}", "UploadLargeFileAsync");

                return key;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during multipart upload for file: {FileName}", file.FileName);
                WatchLogger.LogError($"Error during multipart upload for file: {file.FileName}", "UploadLargeFileAsync", ex.ToString());

                // Abort the multipart upload on error
                try
                {
                    var abortRequest = new AbortMultipartUploadRequest
                    {
                        BucketName = _awsSettings.BucketName,
                        Key = key,
                        UploadId = uploadId
                    };

                    await _s3Client.AbortMultipartUploadAsync(abortRequest);
                    _logger.LogInformation("Aborted multipart upload for file: {FileName}", file.FileName);
                }
                catch (Exception abortEx)
                {
                    _logger.LogError(abortEx, "Error aborting multipart upload for file: {FileName}", file.FileName);
                }

                throw;
            }
        }
        #endregion

        /// <summary>
        /// Uploads a regular file using standard PutObject
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="folderName">The folder name in S3</param>
        /// <returns>The S3 key of the uploaded file</returns>
        private async Task<string> UploadRegularFileAsync(IFormFile file, string folderName)
        {
            // Generate a unique key for the file
            string key = GenerateS3Key(folderName, file.FileName);

            // Create a PutObject request
            using (var stream = file.OpenReadStream())
            {
                var request = new PutObjectRequest
                {
                    BucketName = _awsSettings.BucketName,
                    Key = key,
                    InputStream = stream,
                    ContentType = file.ContentType
                };

                // Upload the file to S3
                await _s3Client.PutObjectAsync(request);
            }

            return key;
        }

        /// <summary>
        /// Uploads a single part of a multipart upload
        /// </summary>
        /// <param name="fileStream">The file stream</param>
        /// <param name="uploadId">The multipart upload ID</param>
        /// <param name="key">The S3 key</param>
        /// <param name="partNumber">The part number</param>
        /// <param name="partSize">The size of this part</param>
        /// <param name="completedParts">List to add completed part info to</param>
        private async Task UploadPartAsync(Stream fileStream, string uploadId, string key, int partNumber, long partSize, List<PartETag> completedParts)
        {
            var buffer = new byte[partSize];
            int bytesRead;

            // Calculate the position for this part
            var position = (partNumber - 1) * AwsConstants.Upload.MultipartPartSizeMB * 1024 * 1024;

            // Create a memory stream for this part
            using var partStream = new MemoryStream();

            // Read the part data
            lock (fileStream)
            {
                fileStream.Seek(position, SeekOrigin.Begin);
                bytesRead = fileStream.Read(buffer, 0, (int)partSize);
            }

            partStream.Write(buffer, 0, bytesRead);
            partStream.Seek(0, SeekOrigin.Begin);

            // Upload the part
            var uploadPartRequest = new UploadPartRequest
            {
                BucketName = _awsSettings.BucketName,
                Key = key,
                UploadId = uploadId,
                PartNumber = partNumber,
                InputStream = partStream,
                PartSize = bytesRead
            };

            var uploadPartResponse = await _s3Client.UploadPartAsync(uploadPartRequest);

            // Add to completed parts list (thread-safe)
            lock (completedParts)
            {
                completedParts.Add(new PartETag
                {
                    PartNumber = partNumber,
                    ETag = uploadPartResponse.ETag
                });
            }
        }

        /// <summary>
        /// Deletes a file from the configured Amazon S3 bucket asynchronously.
        /// </summary>
        /// <remarks>This method attempts to delete the specified file from the Amazon S3 bucket
        /// configured in the application. If an error occurs during the deletion process, the method logs the error and
        /// returns <see langword="false"/>.</remarks>
        /// <param name="key">The key of the file to delete. This is typically the file's unique identifier within the S3 bucket.</param>
        /// <returns><see langword="true"/> if the file was successfully deleted; otherwise, <see langword="false"/>.</returns>
        public async Task<bool> DeleteFileAsync(string key)
        {
            try
            {
                var request = new DeleteObjectRequest
                {
                    BucketName = _awsSettings.BucketName,
                    Key = key
                };

                await _s3Client.DeleteObjectAsync(request);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {Message}", ex.Message);
                WatchLogger.LogError($"Error deleting file from S3: {ex.Message}", "DeleteFileAsync", ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// This method gets a presigned url
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public async Task<string> GetPresignedUrlAsync(string key)
        {
            try
            {
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _awsSettings.BucketName,
                    Key = key,
                    Expires = DateTime.UtcNow.AddMinutes(_awsSettings.PresignedUrlExpiryMinutes)
                };

                return _s3Client.GetPreSignedURL(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned URL: {Message}", ex.Message);
                WatchLogger.LogError($"Error generating presigned URL: {ex.Message}", "GetPresignedUrlAsync", ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// Generates a unique S3 key for storing a file in a specified folder.
        /// </summary>
        /// <remarks>The generated S3 key ensures uniqueness by appending a timestamp and a random
        /// identifier to the sanitized file name. Invalid characters in the file name are replaced with hyphens to
        /// ensure compatibility.</remarks>
        /// <param name="folderName">The name of the folder where the file will be stored. This value is included in the generated key.</param>
        /// <param name="fileName">The original name of the file, including its extension. This value is used to derive the sanitized filename
        /// and extension in the generated key.</param>
        /// <returns>A string representing the generated S3 key, which includes the folder name, a sanitized version of the file
        /// name, a timestamp, a random identifier, and the original file extension.</returns>
        public string GenerateS3Key(string folderName, string fileName)
        {
            // Remove any invalid characters from the filename
            string sanitizedFileName = Path.GetFileNameWithoutExtension(fileName)
                .Replace(" ", "-")
                .Replace("_", "-");

            string fileExtension = Path.GetExtension(fileName);
            string timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");
            string randomPart = Guid.NewGuid().ToString().Substring(0, 8);

            return $"{folderName}/{sanitizedFileName}-{timestamp}-{randomPart}{fileExtension}";
        }

        /// <summary>
        /// Determines the maximum file size based on content type and folder
        /// </summary>
        /// <param name="contentType">The content type of the file</param>
        /// <param name="folderName">The folder where the file will be stored</param>
        /// <returns>Maximum file size in MB</returns>
        private int DetermineMaxFileSize(string contentType, string folderName)
        {
            // First check based on folder
            if (folderName == AwsConstants.Folders.EventBanners ||
                folderName == AwsConstants.Folders.UserProfiles)
            {
                return AwsConstants.MaxFileSizes.Images;
            }
            else if (folderName == AwsConstants.Folders.Documents)
            {
                return AwsConstants.MaxFileSizes.Documents;
            }

            // Then check based on content type
            if (contentType != null)
            {
                if (contentType.StartsWith("image/"))
                {
                    return AwsConstants.MaxFileSizes.Images;
                }
                else if (contentType.StartsWith("video/"))
                {
                    return AwsConstants.MaxFileSizes.Videos;
                }
                else if (contentType == "application/pdf" ||
                         contentType.StartsWith("application/msword") ||
                         contentType.StartsWith("application/vnd.openxmlformats-officedocument") ||
                         contentType.StartsWith("application/vnd.ms-"))
                {
                    return AwsConstants.MaxFileSizes.Documents;
                }
            }

            // Default to the configured value
            return _awsSettings.MaxFileSizeMB;
        }

        /// <summary>
        /// Deletes temporary files from the S3 bucket that are older than the specified number of hours.
        /// </summary>
        /// <remarks>This method performs the cleanup operation in batches to comply with S3's limit of
        /// 1000 objects per delete request. It logs the progress and results of the cleanup operation, including any
        /// errors encountered.</remarks>
        /// <param name="olderThanHours">The age threshold, in hours, for temporary files to be deleted. Files with a last modified date older than
        /// this value will be removed. Must be a positive integer.</param>
        /// <returns>The number of files successfully deleted from the S3 bucket. Returns 0 if no files were deleted or if an
        /// error occurred during the cleanup process.</returns>
        public async Task<int> CleanupTempFilesAsync(int olderThanHours)
        {
            try
            {
                _logger.LogInformation("Starting cleanup of temporary files older than {Hours} hours", olderThanHours);

                // Calculate the cutoff date
                var cutoffDate = DateTime.UtcNow.AddHours(-olderThanHours);

                // List objects in the temp folder
                var listRequest = new ListObjectsV2Request
                {
                    BucketName = _awsSettings.BucketName,
                    Prefix = $"{AwsConstants.Folders.Temp}/"
                };

                ListObjectsV2Response response;
                var keysToDelete = new List<string>();

                do
                {
                    response = await _s3Client.ListObjectsV2Async(listRequest);

                    // Filter objects older than the cutoff date
                    var oldObjects = response.S3Objects
                        .Where(obj => obj.LastModified < cutoffDate)
                        .ToList();

                    // Add keys to the delete list
                    keysToDelete.AddRange(oldObjects.Select(obj => obj.Key));

                    // Set continuation token for next page of results
                    listRequest.ContinuationToken = response.NextContinuationToken;

                } while (response.IsTruncated == true);

                // Delete the old objects in batches
                int deletedCount = 0;

                foreach (var batch in keysToDelete.Chunk(1000)) // S3 allows up to 1000 objects per delete request
                {
                    if (batch.Any())
                    {
                        var deleteRequest = new DeleteObjectsRequest
                        {
                            BucketName = _awsSettings.BucketName,
                            Objects = batch.Select(key => new KeyVersion { Key = key }).ToList()
                        };

                        var deleteResponse = await _s3Client.DeleteObjectsAsync(deleteRequest);
                        deletedCount += deleteResponse.DeletedObjects.Count;
                    }
                }

                _logger.LogInformation("Completed cleanup of temporary files. Deleted {Count} files", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up temporary files: {Message}", ex.Message);
                WatchLogger.LogError($"Error cleaning up temporary files: {ex.Message}", "CleanupTempFilesAsync", ex.ToString());
                return 0;
            }
        }
    }
}
