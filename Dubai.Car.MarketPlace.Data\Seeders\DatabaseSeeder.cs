using Dubai.Car.MarketPlace.Data.Database;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    public static class DatabaseSeeder
    {
        public static async Task SeedDatabaseAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CarContext>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<CarContext>>();

            try
            {
                logger.LogInformation("Starting database seeding process...");

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Seed in order (roles first, then permissions, then role permissions, then users)
                await RoleSeeder.SeedRolesAsync(context, logger);
                await PermissionSeeder.SeedPermissionsAsync(context, logger);
                await RolePermissionSeeder.SeedRolePermissionsAsync(context, logger);
                await UserSeeder.SeedUsersAsync(serviceProvider, logger);
                await AdminUserSeeder.SeedAdminUsersAsync(serviceProvider, logger);

                logger.LogInformation("Database seeding completed successfully.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred during database seeding.");
                throw;
            }
        }

        public static async Task SeedRolesAndPermissionsAsync(IServiceProvider serviceProvider, ILogger logger)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<CarContext>();
                
                logger.LogInformation("Starting roles, permissions, and users seeding...");

                await RoleSeeder.SeedRolesAsync(context, logger);
                await PermissionSeeder.SeedPermissionsAsync(context, logger);
                await RolePermissionSeeder.SeedRolePermissionsAsync(context, logger);
                await UserSeeder.SeedUsersAsync(serviceProvider, logger);
                await AdminUserSeeder.SeedAdminUsersAsync(serviceProvider, logger);

                logger.LogInformation("Roles, permissions, and users seeding completed successfully.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred during roles, permissions, and users seeding.");
                throw;
            }
        }
    }
}
