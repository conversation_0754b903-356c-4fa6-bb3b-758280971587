﻿using AutoMapper;
using Dubai.Car.MarketPlace.Common.Constants;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Net;
using WatchDog;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Implementation of user-related operations
    /// </summary>
    public class UserService : IUserService
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly CarContext _dbContext;
        private readonly IMapper _mapper;
        private readonly ILogger<UserService> _logger;
        private readonly IAwsService _awsService;
        private readonly IJwtService _jwtService;

        /// <summary>
        /// Constructor for UserService
        /// </summary>
        public UserService(
            UserManager<User> userManager,
            RoleManager<Role> roleManager,
            CarContext dbContext,
            IMapper mapper,
            ILogger<UserService> logger,
            IAwsService awsService,
            IJwtService jwtService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _dbContext = dbContext;
            _mapper = mapper;
            _logger = logger;
            _awsService = awsService;
            _jwtService = jwtService;
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<UserProfileResponseDto>> GetUserProfileAsync(long userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    WatchLogger.LogWarning($"User not found: {userId}", "GetUserProfileAsync");
                    return ApiResponse<UserProfileResponseDto>.Failed("User not found", HttpStatusCode.NotFound);
                }

                var userRoles = await _userManager.GetRolesAsync(user);
                var response = _mapper.Map<UserProfileResponseDto>(user);
                response.Role = userRoles.FirstOrDefault() ?? string.Empty;

                // Get profile picture URL if available
                if (!string.IsNullOrEmpty(user.ProfilePictureKey))
                {
                    try
                    {
                        response.ProfilePictureUrl = await _awsService.GetPresignedUrlAsync(user.ProfilePictureKey);
                    }
                    catch (Exception ex)
                    {
                        // Log the error but don't fail the request
                        _logger.LogWarning(ex, "Error generating presigned URL for user {UserId}: {Message}", userId, ex.Message);
                        WatchLogger.LogWarning($"Error generating presigned URL for user {userId}: {ex.Message}", "GetUserProfileAsync");
                        response.ProfilePictureUrl = null;
                    }
                }

                return ApiResponse<UserProfileResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user profile: {Message}", ex.Message);
                WatchLogger.LogError($"Error retrieving user profile: {ex.Message}", "GetUserProfileAsync", ex.ToString());
                return ApiResponse<UserProfileResponseDto>.Failed("An error occurred while retrieving the user profile", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<UserProfileResponseDto>> UpdateUserProfileAsync(long userId, UpdateUserProfileRequestDto model)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                WatchLogger.LogWarning($"User not found: {userId}", "UpdateUserProfileAsync");
                return ApiResponse<UserProfileResponseDto>.Failed("User not found", HttpStatusCode.NotFound);
            }

            // Update basic profile information
            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.Gender = model.Gender;
            user.Designation = model.Designation ?? user.Designation;
            user.PhoneNumber = model.PhoneNumber ?? user.PhoneNumber;
            user.UpdatedOn = DateTime.UtcNow;
            user.IsProfileCompleted = true;

            // Process profile picture if provided
            if (!string.IsNullOrEmpty(model.ProfilePictureBase64) && !string.IsNullOrEmpty(model.ImageFileName))
            {
                try
                {
                    // Convert base64 to IFormFile
                    var imageFile = FileHelper.ConvertBase64ToIFormFile(model.ProfilePictureBase64, model.ImageFileName);

                    // Upload to S3
                    string profilePictureKey = await _awsService.UploadFileAsync(imageFile, AwsConstants.Folders.UserProfiles);

                    // Update the user with the new image key
                    user.ProfilePictureKey = profilePictureKey;
                }
                catch (ArgumentException ex)
                {
                    WatchLogger.LogWarning($"Image processing failed: {ex.Message}", "UpdateUserProfileAsync");
                    return ApiResponse<UserProfileResponseDto>.Failed($"Image processing failed: {ex.Message}", HttpStatusCode.BadRequest);
                }
            }

            // Save changes
            var updateResult = await _userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                var errors = string.Join(", ", updateResult.Errors.Select(e => e.Description));
                WatchLogger.LogWarning($"Failed to update user profile: {errors}", "UpdateUserProfileAsync");
                return ApiResponse<UserProfileResponseDto>.Failed($"Failed to update user profile: {errors}", HttpStatusCode.BadRequest);
            }

            // Get user roles for response
            var userRoles = await _userManager.GetRolesAsync(user);
            var response = _mapper.Map<UserProfileResponseDto>(user);
            response.Role = userRoles.FirstOrDefault() ?? string.Empty;

            // Get profile picture URL if available
            if (!string.IsNullOrEmpty(user.ProfilePictureKey))
            {
                try
                {
                    response.ProfilePictureUrl = await _awsService.GetPresignedUrlAsync(user.ProfilePictureKey);
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the request
                    _logger.LogWarning(ex, "Error generating presigned URL for user {UserId}: {Message}", userId, ex.Message);
                    WatchLogger.LogWarning($"Error generating presigned URL for user {userId}: {ex.Message}", "UpdateUserProfileAsync");
                    response.ProfilePictureUrl = null;
                }
            }

            return ApiResponse<UserProfileResponseDto>.Success(response, "Profile updated successfully");
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<PaginatedResponseDto<UserProfileResponseDto>>> GetAllUsersAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _userManager.Users
                    .OrderByDescending(u => u.CreatedOn);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var users = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var userDtos = new List<UserProfileResponseDto>();
                foreach (var user in users)
                {
                    var userRoles = await _userManager.GetRolesAsync(user);
                    var userDto = _mapper.Map<UserProfileResponseDto>(user);
                    userDto.Role = userRoles.FirstOrDefault() ?? string.Empty;

                    // Get profile picture URL if available
                    if (!string.IsNullOrEmpty(user.ProfilePictureKey))
                    {
                        try
                        {
                            userDto.ProfilePictureUrl = await _awsService.GetPresignedUrlAsync(user.ProfilePictureKey);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but don't fail the request
                            _logger.LogWarning(ex, "Error generating presigned URL for user {UserId}: {Message}", user.Id, ex.Message);
                            WatchLogger.LogWarning($"Error generating presigned URL for user {user.Id}: {ex.Message}", "GetAllUsersAsync");
                            userDto.ProfilePictureUrl = null;
                        }
                    }

                    userDtos.Add(userDto);
                }

                var paginatedResponse = new PaginatedResponseDto<UserProfileResponseDto>
                {
                    Data = userDtos,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalRecords = totalCount,
                    TotalPages = totalPages
                };

                return ApiResponse<PaginatedResponseDto<UserProfileResponseDto>>.Success(paginatedResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users: {Message}", ex.Message);
                WatchLogger.LogError($"Error retrieving users: {ex.Message}", "GetAllUsersAsync", ex.ToString());
                return ApiResponse<PaginatedResponseDto<UserProfileResponseDto>>.Failed("An error occurred while retrieving users", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<UserPermissionsResponseDto>> GetUserPermissionsAsync(long userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    WatchLogger.LogWarning($"User not found: {userId}", "GetUserPermissionsAsync");
                    return ApiResponse<UserPermissionsResponseDto>.Failed("User not found", HttpStatusCode.NotFound);
                }

                // Get user roles
                var userRoles = await _userManager.GetRolesAsync(user);

                // Get user permissions using JWT service
                var permissions = await _jwtService.GetUserPermissionsEnumAsync(user);

                // Create permission details with descriptions
                var permissionDetails = permissions.Select(p => new PermissionDetail
                {
                    Name = p.ToString(),
                    Description = p.GetDescription(),
                    Permission = p
                }).ToList();

                var response = new UserPermissionsResponseDto
                {
                    UserId = user.Id,
                    Email = user.Email ?? string.Empty,
                    Roles = userRoles.ToList(),
                    Permissions = permissions,
                    PermissionDetails = permissionDetails
                };

                return ApiResponse<UserPermissionsResponseDto>.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user permissions: {Message}", ex.Message);
                WatchLogger.LogError($"Error retrieving user permissions: {ex.Message}", "GetUserPermissionsAsync", ex.ToString());
                return ApiResponse<UserPermissionsResponseDto>.Failed("An error occurred while retrieving user permissions", HttpStatusCode.InternalServerError);
            }
        }
    }
}
