using AutoMapper;
using Dubai.Car.MarketPlace.Common.Constants;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Vendor;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using WatchDog;
using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Implementation of vendor-related operations
    /// Updated to use AutoMapper for improved object mapping:
    /// - VendorApplicationRequestDto to VendorApplication entity mapping
    /// - VendorApplication to VendorApplicationResponseDto mapping
    /// - VendorApplication to User entity mapping for vendor account creation
    /// </summary>
    public class VendorService : IVendorService
    {
        private readonly CarContext _dbContext;
        private readonly IAwsService _awsService;
        private readonly IEmailService _emailService;
        private readonly IMapper _mapper;
        private readonly ILogger<VendorService> _logger;
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;

        /// <summary>
        /// Constructor for VendorService
        /// </summary>
        public VendorService(
            CarContext dbContext,
            IAwsService awsService,
            IEmailService emailService,
            IMapper mapper,
            ILogger<VendorService> logger,
            UserManager<User> userManager,
            RoleManager<Role> roleManager)
        {
            _dbContext = dbContext;
            _awsService = awsService;
            _emailService = emailService;
            _mapper = mapper;
            _logger = logger;
            _userManager = userManager;
            _roleManager = roleManager;
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<VendorApplicationResponseDto>> SubmitApplicationAsync(VendorApplicationRequestDto model)
        {
            try
            {
                // Check if an application with the same business email already exists
                var existingApplication = await _dbContext.Set<Vendor>()
                    .FirstOrDefaultAsync(a => a.BusinessEmail.ToLower() == model.BusinessEmail.ToLower());

                if (existingApplication != null)
                {
                    WatchLogger.LogWarning($"Duplicate vendor application for email: {model.BusinessEmail}", "SubmitApplicationAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "An application with this business email already exists",
                        HttpStatusCode.BadRequest);
                }

                // Create a new vendor application using AutoMapper
                var application = _mapper.Map<Vendor>(model);
                application.Status = VendorApplicationStatus.Pending;
                application.SubmissionDate = DateTime.UtcNow;

                // Process and upload trade license document
                try
                {
                    var tradeLicenseFile = FileHelper.ConvertBase64ToIFormFile(
                        model.TradeLicenseDocumentBase64,
                        model.TradeLicenseDocumentFilename);

                    application.TradeLicenseDocumentKey = await _awsService.UploadFileAsync(
                        tradeLicenseFile,
                        AwsConstants.Folders.Documents);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error uploading trade license document: {Message}", ex.Message);
                    WatchLogger.LogError($"Error uploading trade license document: {ex.Message}", "SubmitApplicationAsync", ex.ToString());
                    return ApiResponse<VendorApplicationResponseDto>.Failed("Error uploading trade license document", HttpStatusCode.BadRequest);
                }

                // Process and upload additional documents if any
                if (model.AdditionalDocuments != null && model.AdditionalDocuments.Any())
                {
                    var documentsList = new List<DocumentResponseDto>();
                    var documentKeys = new List<KeyValuePair<string, string>>();

                    foreach (var doc in model.AdditionalDocuments)
                    {
                        try
                        {
                            var docFile = FileHelper.ConvertBase64ToIFormFile(doc.Base64Content, doc.Filename);
                            var docKey = await _awsService.UploadFileAsync(docFile, AwsConstants.Folders.Documents);
                            documentKeys.Add(new KeyValuePair<string, string>(docKey, doc.Description ?? doc.Filename));
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error uploading additional document {Filename}: {Message}", doc.Filename, ex.Message);
                            WatchLogger.LogWarning($"Error uploading additional document {doc.Filename}: {ex.Message}", "SubmitApplicationAsync");
                            // Continue with other documents
                        }
                    }

                    if (documentKeys.Any())
                    {
                        application.AdditionalDocumentKeys = JsonSerializer.Serialize(documentKeys);
                    }
                }

                // Save the application
                await _dbContext.AddAsync(application);
                await _dbContext.SaveChangesAsync();

                // Send confirmation email
                await _emailService.SendVendorApplicationConfirmationEmailAsync(
                    model.BusinessEmail,
                    model.BusinessName,
                    application.Id);

                // Generate the response
                var responseDto = await MapToResponseDto(application);

                return ApiResponse<VendorApplicationResponseDto>.Success(
                    responseDto,
                    "Vendor application submitted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting vendor application: {Message}", ex.Message);
                WatchLogger.LogError($"Error submitting vendor application: {ex.Message}", "SubmitApplicationAsync", ex.ToString());
                return ApiResponse<VendorApplicationResponseDto>.Failed(
                    "An error occurred while submitting the vendor application",
                    HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<VendorApplicationResponseDto>> GetApplicationByIdAsync(Guid applicationId)
        {
            try
            {
                var application = await _dbContext.Set<Vendor>()
                    .Include(a => a.ReviewedByUser)
                    .FirstOrDefaultAsync(a => a.Id == applicationId);

                if (application == null)
                {
                    WatchLogger.LogWarning($"Vendor application not found: {applicationId}", "GetApplicationByIdAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "Vendor application not found",
                        HttpStatusCode.NotFound);
                }

                var responseDto = await MapToResponseDto(application);
                return ApiResponse<VendorApplicationResponseDto>.Success(responseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vendor application: {Message}", ex.Message);
                WatchLogger.LogError($"Error retrieving vendor application: {ex.Message}", "GetApplicationByIdAsync", ex.ToString());
                return ApiResponse<VendorApplicationResponseDto>.Failed(
                    "An error occurred while retrieving the vendor application",
                    HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<PaginatedResponseDto<VendorApplicationResponseDto>>> GetApplicationsAsync(
            VendorApplicationStatus? status = null,
            int pageNumber = 1,
            int pageSize = 10)
        {
            try
            {
                // Start with the basic query
                var query = _dbContext.Set<Vendor>()
                    .Include(a => a.ReviewedByUser)
                    .AsQueryable();

                // Apply status filter if provided
                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var applications = await query
                    .OrderByDescending(a => a.SubmissionDate)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Map to DTOs using AutoMapper for better performance
                var applicationDtos = new List<VendorApplicationResponseDto>();
                foreach (var application in applications)
                {
                    var dto = await MapToResponseDto(application);
                    applicationDtos.Add(dto);
                }

                // Create paginated response
                var paginatedResponse = new PaginatedResponseDto<VendorApplicationResponseDto>
                {
                    Data = applicationDtos,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalRecords = totalCount,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
                };

                return ApiResponse<PaginatedResponseDto<VendorApplicationResponseDto>>.Success(paginatedResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vendor applications: {Message}", ex.Message);
                WatchLogger.LogError($"Error retrieving vendor applications: {ex.Message}", "GetApplicationsAsync", ex.ToString());
                return ApiResponse<PaginatedResponseDto<VendorApplicationResponseDto>>.Failed(
                    "An error occurred while retrieving vendor applications",
                    HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<VendorApplicationResponseDto>> ReviewApplicationAsync(Guid adminUserId, VendorApplicationReviewDto model)
        {
            if (model.NewStatus == VendorApplicationStatus.Approved)
                model.GenerateCredentials = true;

            try
            {
                // Find the application
                var application = await _dbContext.Set<Vendor>()
                    .FirstOrDefaultAsync(a => a.Id == model.ApplicationId);

                if (application == null)
                {
                    WatchLogger.LogWarning($"Vendor application not found: {model.ApplicationId}", "ReviewApplicationAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "Vendor application not found",
                        HttpStatusCode.NotFound);
                }

                // Check if the admin user exists
                var adminUser = await _userManager.FindByIdAsync(adminUserId.ToString());
                if (adminUser == null)
                {
                    WatchLogger.LogWarning($"Admin user not found: {adminUserId}", "ReviewApplicationAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "Admin user not found",
                        HttpStatusCode.NotFound);
                }

                // Update application status
                application.Status = model.NewStatus;
                application.ReviewDate = DateTime.UtcNow;
                application.ReviewedByUserId = Guid.Parse(adminUserId.ToString());
                application.ReviewComments = model.ReviewComments;

                // Handle approved applications
                if (model.NewStatus == VendorApplicationStatus.Approved && model.GenerateCredentials)
                {
                    // Create a new user for the vendor using AutoMapper
                    var vendorUser = _mapper.Map<User>(application);
                    vendorUser.Email = application.BusinessEmail;
                    vendorUser.UserName = application.BusinessEmail;
                    vendorUser.FirstName = application.BusinessName;
                    vendorUser.LastName = application.ContactPersonName;
                    vendorUser.PhoneNumber = application.PhoneNumber;
                    vendorUser.Address = application.BusinessAddress;
                    vendorUser.State = application.City;
                    vendorUser.EmailConfirmed = true; // Auto-confirm email for vendors
                    vendorUser.IsActive = true;

                    // Generate a random password
                    var tempPassword = GenerateRandomPassword();

                    // Create the user
                    var result = await _userManager.CreateAsync(vendorUser, tempPassword);
                    if (!result.Succeeded)
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        WatchLogger.LogWarning($"Failed to create vendor user: {errors}", "ReviewApplicationAsync");
                        return ApiResponse<VendorApplicationResponseDto>.Failed(
                            $"Failed to create vendor user: {errors}",
                            HttpStatusCode.BadRequest);
                    }

                    // Assign the Vendor role
                    var vendorRole = UserRole.Vendor.ToString();
                    if (!await _roleManager.RoleExistsAsync(vendorRole))
                    {
                        await _roleManager.CreateAsync(new Role { Name = vendorRole, NormalizedName = vendorRole.ToUpper() });
                    }

                    await _userManager.AddToRoleAsync(vendorUser, vendorRole);

                    // Link the application to the user
                    application.UserId = vendorUser.Id;

                    // Send approval email with credentials
                    await _emailService.SendVendorApprovalEmailAsync(
                        application.BusinessEmail,
                        application.BusinessName,
                        application.BusinessEmail,
                        tempPassword);
                }
                else if (model.NewStatus == VendorApplicationStatus.UnderReview)
                {
                    // Send under review email
                    await _emailService.SendVendorUnderReviewEmailAsync(
                        application.BusinessEmail,
                        application.BusinessName,
                        application.BusinessEmail);
                }
                else if (model.NewStatus == VendorApplicationStatus.Rejected)
                {
                    // Send rejection email
                    await _emailService.SendVendorRejectionEmailAsync(
                        application.BusinessEmail,
                        application.BusinessName,
                        model.ReviewComments);
                }
                else if (model.NewStatus == VendorApplicationStatus.AdditionalInfoRequired)
                {
                    // Send additional info request email
                    await _emailService.SendAdditionalInfoRequestEmailAsync(
                        application.BusinessEmail,
                        application.BusinessName,
                        model.ReviewComments ?? "Please contact us for more information about your application.");
                }

                // Save changes
                await _dbContext.SaveChangesAsync();

                // Return the updated application
                var responseDto = await MapToResponseDto(application);

                return ApiResponse<VendorApplicationResponseDto>.Success(
                    responseDto,
                    $"Vendor application {model.NewStatus.ToString().ToLower()} successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reviewing vendor application: {Message}", ex.Message);
                WatchLogger.LogError($"Error reviewing vendor application: {ex.Message}", "ReviewApplicationAsync", ex.ToString());
                return ApiResponse<VendorApplicationResponseDto>.Failed(
                    "An error occurred while reviewing the vendor application",
                    HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<VendorApplicationResponseDto>> GetVendorOwnApplicationAsync(Guid userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    WatchLogger.LogWarning($"User not found: {userId}", "GetVendorOwnApplicationAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "User not found",
                        HttpStatusCode.NotFound);
                }

                // Check if user has the Vendor role
                var isVendor = await _userManager.IsInRoleAsync(user, "Vendor");
                if (!isVendor)
                {
                    WatchLogger.LogWarning($"User {userId} is not a vendor", "GetVendorOwnApplicationAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "User is not a vendor",
                        HttpStatusCode.Forbidden);
                }

                // Find the application by user ID or email
                var application = await _dbContext.Set<Vendor>()
                    .Include(a => a.ReviewedByUser)
                    .FirstOrDefaultAsync(a =>
                        a.UserId == Guid.Parse(userId.ToString()) ||
                        (user.Email != null && a.BusinessEmail.ToLower() == user.Email.ToLower()));

                if (application == null)
                {
                    WatchLogger.LogWarning($"No application found for vendor: {userId}", "GetVendorOwnApplicationAsync");
                    return ApiResponse<VendorApplicationResponseDto>.Failed(
                        "No application found for this vendor",
                        HttpStatusCode.NotFound);
                }

                var responseDto = await MapToResponseDto(application);

                return ApiResponse<VendorApplicationResponseDto>.Success(responseDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vendor's own application: {Message}", ex.Message);
                WatchLogger.LogError($"Error retrieving vendor's own application: {ex.Message}", "GetVendorOwnApplicationAsync", ex.ToString());
                return ApiResponse<VendorApplicationResponseDto>.Failed(
                    "An error occurred while retrieving the vendor application",
                    HttpStatusCode.InternalServerError);
            }
        }

        #region Helper Methods

        private async Task<VendorApplicationResponseDto> MapToResponseDto(Vendor application)
        {
            // Use AutoMapper for basic mapping
            var responseDto = _mapper.Map<VendorApplicationResponseDto>(application);

            // Handle custom mapping for reviewer name
            responseDto.ReviewerName = application.ReviewedByUser != null
                ? $"{application.ReviewedByUser.FirstName} {application.ReviewedByUser.LastName}"
                : null;

            // Generate URL for trade license document if available
            if (!string.IsNullOrEmpty(application.TradeLicenseDocumentKey))
            {
                try
                {
                    responseDto.TradeLicenseDocumentUrl = await _awsService.GetPresignedUrlAsync(application.TradeLicenseDocumentKey);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error generating trade license document URL: {Message}", ex.Message);
                    WatchLogger.LogWarning($"Error generating trade license document URL: {ex.Message}", "MapToResponseDto");
                    responseDto.TradeLicenseDocumentUrl = null;
                }
            }

            // Generate URLs for additional documents if available
            if (!string.IsNullOrEmpty(application.AdditionalDocumentKeys))
            {
                try
                {
                    var documentKeys = JsonSerializer.Deserialize<List<KeyValuePair<string, string>>>(application.AdditionalDocumentKeys);
                    if (documentKeys != null && documentKeys.Any())
                    {
                        responseDto.AdditionalDocuments = new List<DocumentResponseDto>();
                        foreach (var doc in documentKeys)
                        {
                            try
                            {
                                var url = await _awsService.GetPresignedUrlAsync(doc.Key);
                                responseDto.AdditionalDocuments.Add(new DocumentResponseDto
                                {
                                    Url = url,
                                    Filename = Path.GetFileName(doc.Key),
                                    Description = doc.Value
                                });
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Error generating additional document URL: {Message}", ex.Message);
                                WatchLogger.LogWarning($"Error generating additional document URL: {ex.Message}", "MapToResponseDto");
                                // Continue with other documents
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error parsing additional document keys: {Message}", ex.Message);
                    WatchLogger.LogWarning($"Error parsing additional document keys: {ex.Message}", "MapToResponseDto");
                }
            }

            return responseDto;
        }

        private static string GenerateRandomPassword(int length = 12)
        {
            // Define character sets for password complexity
            const string uppercaseChars = "ABCDEFGHJKLMNPQRSTUVWXYZ";
            const string lowercaseChars = "abcdefghijkmnopqrstuvwxyz";
            const string numericChars = "23456789";
            const string specialChars = "!@#$%^&*";

            // Create random number generator
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[length];
            rng.GetBytes(bytes);

            // Ensure at least one character from each set
            var password = new StringBuilder(length);
            password.Append(uppercaseChars[bytes[0] % uppercaseChars.Length]);
            password.Append(lowercaseChars[bytes[1] % lowercaseChars.Length]);
            password.Append(numericChars[bytes[2] % numericChars.Length]);
            password.Append(specialChars[bytes[3] % specialChars.Length]);

            // Fill the rest of the password
            var allChars = uppercaseChars + lowercaseChars + numericChars + specialChars;
            for (var i = 4; i < length; i++)
            {
                password.Append(allChars[bytes[i] % allChars.Length]);
            }

            // Shuffle the password
            var shuffled = new string(password.ToString().OrderBy(c => Guid.NewGuid()).ToArray());

            return shuffled;
        }

        #endregion
    }
}
