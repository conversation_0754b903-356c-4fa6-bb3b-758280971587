<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.Hangfire" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.Redis" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
		<PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
		<PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
		<PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.HealthChecks" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.11" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.5" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
		<PackageReference Include="WatchDog.NET" Version="1.4.11" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Dubai.Car.MarketPlace.Common\Dubai.Car.MarketPlace.Common.csproj" />
	  <ProjectReference Include="..\Dubai.Car.MarketPlace.Data\Dubai.Car.MarketPlace.Data.csproj" />
	  <ProjectReference Include="..\Dubai.Car.MarketPlace.Logic\Dubai.Car.MarketPlace.Logic.csproj" />
	</ItemGroup>

</Project>
