using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Car
{
    /// <summary>
    /// Response DTO for car listing
    /// </summary>
    public class CarResponseDto
    {
        /// <summary>
        /// The unique ID of the car
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The model name of the car
        /// </summary>
        public string Model { get; set; } = default!;

        /// <summary>
        /// The manufacturing year of the car
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// The price of the car in AED
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// The mileage/odometer reading in kilometers
        /// </summary>
        public int? Mileage { get; set; }

        /// <summary>
        /// The color of the car
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// The VIN (Vehicle Identification Number)
        /// </summary>
        public string? VIN { get; set; }

        /// <summary>
        /// The license plate number
        /// </summary>
        public string? LicensePlate { get; set; }

        /// <summary>
        /// The engine size in liters
        /// </summary>
        public decimal? EngineSize { get; set; }

        /// <summary>
        /// The transmission type
        /// </summary>
        public TransmissionType? Transmission { get; set; }

        /// <summary>
        /// The number of doors
        /// </summary>
        public int? NumberOfDoors { get; set; }

        /// <summary>
        /// The number of seats
        /// </summary>
        public int? NumberOfSeats { get; set; }

        /// <summary>
        /// The condition of the car
        /// </summary>
        public CarCondition Condition { get; set; }

        /// <summary>
        /// The current status of the car listing
        /// </summary>
        public CarStatus Status { get; set; }

        /// <summary>
        /// Description of the car
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Features of the car
        /// </summary>
        public List<string>? Features { get; set; }

        /// <summary>
        /// Image URLs for car photos
        /// </summary>
        public List<string>? ImageUrls { get; set; }

        /// <summary>
        /// The main/primary image URL
        /// </summary>
        public string? PrimaryImageUrl { get; set; }

        /// <summary>
        /// Whether the car is featured
        /// </summary>
        public bool IsFeatured { get; set; }

        /// <summary>
        /// Whether the car is sold
        /// </summary>
        public bool IsSold { get; set; }

        /// <summary>
        /// The date when the car was sold
        /// </summary>
        public DateTime? SoldDate { get; set; }

        /// <summary>
        /// Number of views this car listing has received
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// Number of inquiries this car listing has received
        /// </summary>
        public int InquiryCount { get; set; }

        /// <summary>
        /// The date when the listing expires
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// The date when the car was created
        /// </summary>
        public DateTime CreatedOn { get; set; }

        /// <summary>
        /// The date when the car was last modified
        /// </summary>
        public DateTime? ModifiedOn { get; set; }

        // Related entities
        /// <summary>
        /// The brand of the car
        /// </summary>
        public BrandResponseDto Brand { get; set; } = default!;

        /// <summary>
        /// The body type of the car
        /// </summary>
        public BodyTypeResponseDto BodyType { get; set; } = default!;

        /// <summary>
        /// The fuel type of the car
        /// </summary>
        public FuelTypeResponseDto FuelType { get; set; } = default!;

        /// <summary>
        /// The vendor who listed the car (if applicable)
        /// </summary>
        public VendorResponseDto? Vendor { get; set; }

        /// <summary>
        /// The user who listed the car
        /// </summary>
        public UserResponseDto User { get; set; } = default!;

        /// <summary>
        /// Specification values for this car
        /// </summary>
        public List<CarSpecificationValueResponseDto>? SpecificationValues { get; set; }
    }

    /// <summary>
    /// Simplified response DTO for car listing (for lists)
    /// </summary>
    public class CarSummaryResponseDto
    {
        /// <summary>
        /// The unique ID of the car
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The model name of the car
        /// </summary>
        public string Model { get; set; } = default!;

        /// <summary>
        /// The manufacturing year of the car
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// The price of the car in AED
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// The mileage/odometer reading in kilometers
        /// </summary>
        public int? Mileage { get; set; }

        /// <summary>
        /// The color of the car
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// The condition of the car
        /// </summary>
        public CarCondition Condition { get; set; }

        /// <summary>
        /// The current status of the car listing
        /// </summary>
        public CarStatus Status { get; set; }

        /// <summary>
        /// The main/primary image URL
        /// </summary>
        public string? PrimaryImageUrl { get; set; }

        /// <summary>
        /// Whether the car is featured
        /// </summary>
        public bool IsFeatured { get; set; }

        /// <summary>
        /// Whether the car is sold
        /// </summary>
        public bool IsSold { get; set; }

        /// <summary>
        /// Number of views this car listing has received
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// The date when the car was created
        /// </summary>
        public DateTime CreatedOn { get; set; }

        // Related entities (simplified)
        /// <summary>
        /// The brand name
        /// </summary>
        public string BrandName { get; set; } = default!;

        /// <summary>
        /// The body type name
        /// </summary>
        public string BodyTypeName { get; set; } = default!;

        /// <summary>
        /// The fuel type name
        /// </summary>
        public string FuelTypeName { get; set; } = default!;

        /// <summary>
        /// The vendor name (if applicable)
        /// </summary>
        public string? VendorName { get; set; }
    }


}
