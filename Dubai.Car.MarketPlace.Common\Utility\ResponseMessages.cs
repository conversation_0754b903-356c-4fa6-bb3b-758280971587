namespace Dubai.Car.MarketPlace.Common.Utility;

public static class ResponseMessages
{
    // General responses
    public const string GeneralExceptionResponse = "An error occurred while processing your request. Please try again later.";
    public const string NotFoundResponse = "The requested resource was not found.";
    public const string BadRequestResponse = "The request was invalid or cannot be served. Please check the request and try again.";
    public const string UnauthorizedResponse = "You are not authorized to access this resource.";
    public const string ForbiddenResponse = "You do not have permission to access this resource.";
    public const string InternalServerErrorResponse = "An internal server error occurred. Please try again later.";
    public const string SuccessResponse = "The operation was successful.";

    // Authentication responses
    public const string UserAlreadyExistsResponse = "User with this email already exists.";
    public const string RegistrationSuccessResponse = "Registration successful. Please check your email to verify your account.";
    public const string InvalidVerificationRequestResponse = "Invalid verification request.";
    public const string EmailAlreadyVerifiedResponse = "Email already verified.";
    public const string EmailVerificationSuccessResponse = "Email verified successfully. You can now log in.";
    public const string InvalidCredentialsResponse = "Invalid email or password.";
    public const string AccountNotVerifiedResponse = "Please verify your email before logging in.";
    public const string EmailVerificationResentResponse = "A new verification email has been sent. Please check your inbox.";
    public const string LoginSuccessResponse = "Login successful.";
    public const string LogoutSuccessResponse = "Logout successful.";
    public const string PasswordsDoNotMatchResponse = "Password and confirmation password do not match.";

    // Password management responses
    public const string PasswordChangedSuccessResponse = "Password changed successfully.";
    public const string CurrentPasswordIncorrectResponse = "Current password is incorrect.";
    public const string PasswordResetEmailSentResponse = "If your email exists in our system, you will receive a password reset link shortly.";
    public const string PasswordResetSuccessResponse = "Password has been reset successfully. You can now log in with your new password.";
    public const string InvalidPasswordResetTokenResponse = "Invalid or expired password reset token.";

    // Token management responses
    public const string InvalidRefreshTokenResponse = "Invalid refresh token.";
    public const string ExpiredRefreshTokenResponse = "Refresh token has expired.";
    public const string TokenRefreshedSuccessResponse = "Token refreshed successfully.";
    public const string InvalidTokenResponse = "Invalid token.";
    public const string TokenBlacklistedResponse = "Token has been blacklisted.";

    public static string TotalAmountMismatch(decimal total, decimal itemsTotal) => 
        $"Total amount ({total}) must match the sum of all payment items ({itemsTotal})";
        
    public static string ExceedsBalance(string studentName, decimal amount, decimal balance) => 
        $"Amount paid for student {studentName} ({amount}) cannot exceed the remaining balance of {balance}";
}