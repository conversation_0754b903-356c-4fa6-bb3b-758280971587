﻿namespace Dubai.Car.MarketPlace.Common.Enums
{
    public class AuthEnums
    {
        public enum Permissions : int
        {
            /// <summary>
            /// Permission to view the dashboard
            /// </summary>
            DashboardView = 1,
            /// <summary>
            /// Permission to manage users
            /// </summary>
            UserManagement = 2,
            /// <summary>
            /// Permission to manage roles
            /// </summary>
            RoleManagement = 3,
            /// <summary>
            /// Permission to manage permissions
            /// </summary>
            PermissionManagement = 4,
            /// <summary>
            /// Permission to manage settings
            /// </summary>
            SettingsManagement = 5,
            /// <summary>
            /// Permission to view reports
            /// </summary>
            ReportView = 6
        }

        // User Types
        public enum UserTypes
        { 
            /// <summary>
            /// Users that are just customers
            /// </summary>
            Customer = 1,
            /// <summary>
            /// Represents a private seller in the context of a transaction or marketplace.
            /// </summary>
            PrivateSeller = 2,
            /// <summary>
            /// Represents a vendor within the system.
            /// </summary>
            Vendor = 3,
            /// <summary>
            /// Represents an administrative user with elevated permissions.
            /// </summary>
            Admin = 4,
        }
    }
}
