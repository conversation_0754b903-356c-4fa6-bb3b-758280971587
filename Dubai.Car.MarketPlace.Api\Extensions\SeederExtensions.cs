using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Seeders;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Provides extension methods for configuring and executing database seeding operations.
    /// </summary>
    /// <remarks>This static class contains methods to add database seeding services to the dependency
    /// injection container and to seed the database with initial data, roles, and permissions. These methods are
    /// designed to be used during application startup or initialization.</remarks>
    public static class SeederExtensions
    {
        /// <summary>
        /// Adds database seeding services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddDatabaseSeeding(this IServiceCollection services)
        {
            // Add any seeding-related services here if needed
            return services;
        }

        /// <summary>
        /// Seeds the database with initial data
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task SeedDatabaseAsync(this IServiceProvider serviceProvider)
        {
            await DatabaseSeeder.SeedDatabaseAsync(serviceProvider);
        }

        /// <summary>
        /// Seeds roles and permissions only
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task SeedRolesAndPermissionsAsync(this IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<CarContext>>();

            await DatabaseSeeder.SeedRolesAndPermissionsAsync(serviceProvider, logger);
        }

        /// <summary>
        /// Seeds admin users only
        /// </summary>
        /// <param name="serviceProvider">The service provider</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task SeedAdminUsersAsync(this IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<CarContext>>();

            await AdminUserSeeder.SeedAdminUsersAsync(serviceProvider, logger);
        }
    }
}
