using System;
using System.Text.Json;
using System.Threading.Tasks;
using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl;

/// <summary>
/// Redis cache service implementation with advanced features
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _cache;
    private readonly IConnectionMultiplexer _redisConnection;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly CacheConfiguration _cacheConfig;

    public RedisCacheService(
        IDistributedCache cache,
        IConnectionMultiplexer redisConnection,
        ILogger<RedisCacheService> logger,
        CacheConfiguration cacheConfig)
    {
        _cache = cache;
        _redisConnection = redisConnection;
        _logger = logger;
        _cacheConfig = cacheConfig;
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            var cachedValue = await _cache.GetStringAsync(key);
            return cachedValue == null ? null : JsonSerializer.Deserialize<T>(cachedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving value from cache for key {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, int expirationMinutes = 30) where T : class
    {
        try
        {
            // Check cache size before adding new item
            await ManageCacheSize();

            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(expirationMinutes)
            };

            var serializedValue = JsonSerializer.Serialize(value);
            await _cache.SetStringAsync(key, serializedValue, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting value in cache for key {Key}", key);
        }
    }

    public async Task RemoveAsync(string key)
    {
        try
        {
            await _cache.RemoveAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing value from cache for key {Key}", key);
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            return await _cache.GetAsync(key) != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking existence in cache for key {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Manages cache size by implementing eviction strategy
    /// </summary>
    private async Task ManageCacheSize()
    {
        try
        {
            var server = _redisConnection.GetServer(_redisConnection.GetEndPoints()[0]);
            var database = _redisConnection.GetDatabase();

            // Get current memory usage
            var info = await server.InfoAsync("memory");
            var memorySection = info.FirstOrDefault(x => x.Key == "memory");
            var usedMemoryStr = memorySection?.FirstOrDefault(kv => kv.Key == "used_memory_human").Value;

            if (usedMemoryStr != null && TryParseMemoryToMB(usedMemoryStr, out double usedMemoryMB))
            {
                if (usedMemoryMB >= _cacheConfig.MaxCacheSizeMB)
                {
                    _logger.LogWarning("Cache size ({UsedMemoryMB} MB) exceeded limit of {MaxCacheSizeMB} MB. Implementing eviction strategy.",
                        usedMemoryMB, _cacheConfig.MaxCacheSizeMB);

                    // Calculate how many keys to remove
                    var allKeys = server.Keys().ToArray();
                    var keysToRemoveCount = (int)(allKeys.Length * (_cacheConfig.EvictionPercentage / 100.0));

                    // Remove oldest keys based on TTL
                    foreach (var key in allKeys.Take(keysToRemoveCount))
                    {
                        await database.KeyDeleteAsync(key);
                    }

                    _logger.LogInformation("Removed {Count} keys from cache", keysToRemoveCount);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error managing cache size");
        }
    }

    /// <summary>
    /// Parses Redis memory string to MB
    /// </summary>
    private bool TryParseMemoryToMB(string memoryString, out double memoryMB)
    {
        memoryMB = 0;
        try
        {
            var value = double.Parse(new string(memoryString.TakeWhile(c => char.IsDigit(c) || c == '.').ToArray()));
            var unit = memoryString.Skip(value.ToString().Length).Take(1).First();

            memoryMB = unit switch
            {
                'K' => value / 1024,
                'M' => value,
                'G' => value * 1024,
                _ => value / (1024 * 1024)
            };

            return true;
        }
        catch
        {
            return false;
        }
    }
} 