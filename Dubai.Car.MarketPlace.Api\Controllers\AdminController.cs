using Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Dubai.Car.MarketPlace.Logic.Services.Impl;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Security.Claims;
using WatchDog;
using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// Controller for super admin operations
    /// </summary>
    //[Authorize(Roles = nameof(SystemRoles.SuperAdmin))]
    [Authorize]
    public class AdminController : BaseController
    {
        private readonly IAdminService _adminService;
        private readonly ILogger<AdminController> _logger;
        private readonly ICarService _carService;
        private readonly ICarLookupService _carLookupService;
        private readonly ICarSpecificationService _carSpecificationService;

        /// <summary>
        /// Constructor for AdminController
        /// </summary>
        /// <param name="adminService">Admin service</param>
        /// <param name="logger">Logger</param>
        /// <param name="carLookupService"></param>
        /// <param name="carService"></param>
        /// <param name="carSpecificationService"></param>
        public AdminController(
            IAdminService adminService,
            ILogger<AdminController> logger,
            ICarService carService,
            ICarLookupService carLookupService,
            ICarSpecificationService carSpecificationService)
        {
            _adminService = adminService;
            _logger = logger;
            _carService = carService;
            _carLookupService = carLookupService;
            _carSpecificationService = carSpecificationService;
        }

        /// <summary>
        /// Create a new admin user
        /// </summary>
        /// <param name="model">Create admin user request</param>
        /// <returns>Created admin user information</returns>
        [HttpPost("create-admin")]
        [ProducesResponseType(typeof(ApiResponse<AdminUserResponseDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 409)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> CreateAdminUser([FromBody] CreateAdminUserRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for admin user creation: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "CreateAdminUser");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Creating admin user: {model.Email} by super admin: {currentUserId}", "CreateAdminUser");

            var result = await _adminService.CreateAdminUserAsync(model);

            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetAdminUserById), new { userId = result.Data!.Id }, result);
            }

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Update an existing admin user
        /// </summary>
        /// <param name="userId">User ID to update</param>
        /// <param name="model">Update admin user request</param>
        /// <returns>Updated admin user information</returns>
        [HttpPut("update-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<AdminUserResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> UpdateAdminUser(Guid userId, [FromBody] UpdateAdminUserRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for admin user update: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "UpdateAdminUser");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Updating admin user: {userId} by super admin: {currentUserId}", "UpdateAdminUser");

            var result = await _adminService.UpdateAdminUserAsync(userId, model);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Delete an admin user
        /// </summary>
        /// <param name="userId">User ID to delete</param>
        /// <returns>Delete operation result</returns>
        [HttpDelete("delete-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> DeleteAdminUser(Guid userId)
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Deleting admin user: {userId} by super admin: {currentUserId}", "DeleteAdminUser");

            var result = await _adminService.DeleteAdminUserAsync(userId);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get an admin user by ID
        /// </summary>
        /// <param name="userId">User ID to retrieve</param>
        /// <returns>Admin user information</returns>
        [HttpGet("get-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<AdminUserResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAdminUserById(Guid userId)
        {
            var result = await _adminService.GetAdminUserByIdAsync(userId);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all admin users with pagination
        /// </summary>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>List of admin users</returns>
        [HttpGet("get-all-admins")]
        [ProducesResponseType(typeof(ApiResponse<List<AdminUserResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllAdminUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            if (page < 1 || pageSize < 1 || pageSize > 100)
            {
                WatchLogger.LogWarning($"Invalid pagination parameters: page={page}, pageSize={pageSize}", "GetAllAdminUsers");
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = "Invalid pagination parameters. Page must be >= 1 and pageSize must be between 1 and 100."
                });
            }

            var result = await _adminService.GetAllAdminUsersAsync(page, pageSize);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Lock or unlock an admin user
        /// </summary>
        /// <param name="userId">User ID to lock/unlock</param>
        /// <param name="model">Lock admin user request</param>
        /// <returns>Lock operation result</returns>
        [HttpPut("lock-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> LockUnlockAdminUser(Guid userId, [FromBody] LockAdminUserRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for admin user lock/unlock: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "LockUnlockAdminUser");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var action = model.IsLocked ? "locking" : "unlocking";
            WatchLogger.Log($"{action} admin user: {userId} by super admin: {currentUserId}", "LockUnlockAdminUser");

            var result = await _adminService.LockUnlockAdminUserAsync(userId, model);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all available permissions
        /// </summary>
        /// <returns>List of available permissions</returns>
        [HttpGet("get-permissions")]
        [ProducesResponseType(typeof(ApiResponse<List<PermissionResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllPermissions()
        {
            var result = await _adminService.GetAllPermissionsAsync();

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Assign or remove permissions from a role
        /// </summary>
        /// <param name="model">Assign role permissions request</param>
        /// <returns>Assignment operation result</returns>
        [HttpPut("assign-role-permissions")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> AssignRolePermissions([FromBody] AssignRolePermissionsRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for role permissions assignment: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "AssignRolePermissions");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Assigning role permissions for role: {model.RoleId} by super admin: {currentUserId}", "AssignRolePermissions");

            var result = await _adminService.AssignRolePermissionsAsync(model);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all roles with their permissions
        /// </summary>
        /// <returns>List of roles with permissions</returns>
        [HttpGet("get-roles-with-permissions")]
        [ProducesResponseType(typeof(ApiResponse<List<RoleWithPermissionsResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllRolesWithPermissions()
        {
            var result = await _adminService.GetAllRolesWithPermissionsAsync();

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get a role by ID with its permissions
        /// </summary>
        /// <param name="roleId">Role ID to retrieve</param>
        /// <returns>Role with permissions</returns>
        [HttpGet("get-role-with-permissions/{roleId}")]
        [ProducesResponseType(typeof(ApiResponse<RoleWithPermissionsResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetRoleByIdWithPermissions(Guid roleId)
        {
            var result = await _adminService.GetRoleByIdWithPermissionsAsync(roleId);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get complete dashboard data including stats, vendor applications, support tickets, and revenue
        /// </summary>
        /// <returns>Complete dashboard data</returns>
        [HttpGet("dashboard")]
        [ProducesResponseType(typeof(ApiResponse<DashboardResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetDashboardData()
        {
            var result = await _adminService.GetDashboardDataAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get dashboard statistics only (total users, active vendors, listings, revenue)
        /// </summary>
        /// <returns>Dashboard statistics</returns>
        [HttpGet("dashboard/stats")]
        [ProducesResponseType(typeof(ApiResponse<DashboardStatsResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetDashboardStats()
        {
            var result = await _adminService.GetDashboardStatsAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get pending vendor applications for dashboard
        /// </summary>
        /// <param name="count">Number of applications to return (default: 5)</param>
        /// <returns>Pending vendor applications</returns>
        [HttpGet("dashboard/pending-vendor-applications")]
        [ProducesResponseType(typeof(ApiResponse<List<DashboardVendorApplicationResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetPendingVendorApplications([FromQuery] int count = 5)
        {
            if (count < 1 || count > 50)
            {
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = "Count must be between 1 and 50."
                });
            }

            var result = await _adminService.GetPendingVendorApplicationsAsync(count);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get platform revenue data for dashboard
        /// </summary>
        /// <returns>Platform revenue data</returns>
        [HttpGet("dashboard/revenue")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetPlatformRevenueData()
        {
            var result = await _adminService.GetPlatformRevenueDataAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        #region Car Management

        /// <summary>
        /// Gets all cars with admin filters
        /// </summary>
        /// <param name="request">Filter request</param>
        /// <returns>Paginated car listings</returns>
        [HttpGet("car/get-all")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>>> GetAllCars([FromQuery] CarFilterRequest request)
        {
            request.IncludeDeleted = true; // Admin can see deleted cars
            var result = await _carService.GetCarsAsync(request);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets car statistics for admin dashboard
        /// </summary>
        /// <returns>Car statistics</returns>
        [HttpGet("car/get-statistics")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarStatisticsResponseDto>>> GetCarStatistics()
        {
            var result = await _carService.GetCarStatisticsAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates car status (approve, reject, suspend, etc.)
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Status update request</param>
        /// <returns>Updated car details</returns>
        [HttpPatch("car/update-status/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarResponseDto>>> UpdateCarStatus(Guid id, [FromBody] UpdateCarStatusRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<CarResponseDto>.Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            var updateRequest = new UpdateCarRequest
            {
                Status = request.Status
            };

            var result = await _carService.UpdateCarAsync(id, updateRequest, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Toggles car featured status
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Featured toggle request</param>
        /// <returns>Updated car details</returns>
        [HttpPatch("car/toggle-featured/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarResponseDto>>> ToggleCarFeatured(Guid id, [FromBody] ToggleFeaturedRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<CarResponseDto>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            var updateRequest = new UpdateCarRequest
            {
                IsFeatured = request.IsFeatured
            };

            var result = await _carService.UpdateCarAsync(id, updateRequest, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Approves a car listing (Admin only)
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Approval request</param>
        /// <returns>Success response</returns>
        [HttpPost("car/approve/{id}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> ApproveCar(Guid id, [FromBody] ApproveCarRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<bool>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            if (!ModelState.IsValid)
            {
                var errors = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ApiResponse<bool>.Failed($"Validation failed: {errors}", System.Net.HttpStatusCode.BadRequest));
            }

            var result = await _carService.ApproveCarAsync(id, request, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Rejects a car listing (Admin only)
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Rejection request</param>
        /// <returns>Success response</returns>
        [HttpPost("car/reject/{id}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> RejectCar(Guid id, [FromBody] RejectCarRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<bool>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            if (!ModelState.IsValid)
            {
                var errors = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ApiResponse<bool>.Failed($"Validation failed: {errors}", System.Net.HttpStatusCode.BadRequest));
            }

            var result = await _carService.RejectCarAsync(id, request, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion

        #region Brand Management

        /// <summary>
        /// Gets all brands including inactive ones
        /// </summary>
        /// <returns>List of brands</returns>
        [HttpGet("car/get-all-brands")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<BrandResponseDto>>>> GetAllBrands()
        {
            var result = await _carLookupService.GetBrandsAsync(includeInactive: true);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a new brand
        /// </summary>
        /// <param name="request">Brand creation request</param>
        /// <returns>Created brand</returns>
        [HttpPost("car/create-brand")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<BrandResponseDto>>> CreateBrand([FromBody] CreateBrandRequest request)
        {
            var result = await _carLookupService.CreateBrandAsync(
                request.Name,
                request.Description,
                request.CountryOfOrigin,
                request.LogoImageKey);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates an existing brand
        /// </summary>
        /// <param name="id">Brand ID</param>
        /// <param name="request">Brand update request</param>
        /// <returns>Updated brand</returns>
        [HttpPut("car/update-brand/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<BrandResponseDto>>> UpdateBrand(Guid id, [FromBody] UpdateBrandRequest request)
        {
            var result = await _carLookupService.UpdateBrandAsync(
                id,
                request.Name,
                request.Description,
                request.CountryOfOrigin,
                request.LogoImageKey,
                request.IsActive);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Deletes a brand
        /// </summary>
        /// <param name="id">Brand ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("car/delete-brand/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteBrand(Guid id)
        {
            var result = await _carLookupService.DeleteBrandAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion

        #region Body Type Management

        /// <summary>
        /// Gets all body types including inactive ones
        /// </summary>
        /// <returns>List of body types</returns>
        [HttpGet("car/get-all-body-types")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<BodyTypeResponseDto>>>> GetAllBodyTypes()
        {
            var result = await _carLookupService.GetBodyTypesAsync(includeInactive: true);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a new body type
        /// </summary>
        /// <param name="request">Body type creation request</param>
        /// <returns>Created body type</returns>
        [HttpPost("car/create-body-type")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<BodyTypeResponseDto>>> CreateBodyType([FromBody] CreateBodyTypeRequest request)
        {
            var result = await _carLookupService.CreateBodyTypeAsync(
                request.Name,
                request.Description,
                request.IconImageKey);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates an existing body type
        /// </summary>
        /// <param name="id">Body type ID</param>
        /// <param name="request">Body type update request</param>
        /// <returns>Updated body type</returns>
        [HttpPut("car/update-body-type/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<BodyTypeResponseDto>>> UpdateBodyType(Guid id, [FromBody] UpdateBodyTypeRequest request)
        {
            var result = await _carLookupService.UpdateBodyTypeAsync(
                id,
                request.Name,
                request.Description,
                request.IconImageKey,
                request.IsActive);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Deletes a body type
        /// </summary>
        /// <param name="id">Body type ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("car/delete-body-type/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteBodyType(Guid id)
        {
            var result = await _carLookupService.DeleteBodyTypeAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion

        #region Fuel Type Management

        /// <summary>
        /// Gets all fuel types including inactive ones
        /// </summary>
        /// <returns>List of fuel types</returns>
        [HttpGet("car/get--all-fuel-type")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<FuelTypeResponseDto>>>> GetAllFuelTypes()
        {
            var result = await _carLookupService.GetFuelTypesAsync(includeInactive: true);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a new fuel type
        /// </summary>
        /// <param name="request">Fuel type creation request</param>
        /// <returns>Created fuel type</returns>
        [HttpPost("car/create-fuel-type")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<FuelTypeResponseDto>>> CreateFuelType([FromBody] CreateFuelTypeRequest request)
        {
            var result = await _carLookupService.CreateFuelTypeAsync(
                request.Name,
                request.Description,
                request.IconImageKey,
                request.EnvironmentalRating);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates an existing fuel type
        /// </summary>
        /// <param name="id">Fuel type ID</param>
        /// <param name="request">Fuel type update request</param>
        /// <returns>Updated fuel type</returns>
        [HttpPut("car/update-fuel-type/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<FuelTypeResponseDto>>> UpdateFuelType(Guid id, [FromBody] UpdateFuelTypeRequest request)
        {
            var result = await _carLookupService.UpdateFuelTypeAsync(
                id,
                request.Name,
                request.Description,
                request.IconImageKey,
                request.EnvironmentalRating,
                request.IsActive);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Deletes a fuel type
        /// </summary>
        /// <param name="id">Fuel type ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("car/delete-fuel-type/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteFuelType(Guid id)
        {
            var result = await _carLookupService.DeleteFuelTypeAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion

        #region Private Method
        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }
        #endregion
    }
}