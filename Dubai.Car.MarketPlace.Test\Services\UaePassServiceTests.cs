using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Logic.Services.Impl;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System.Net;
using System.Text;
using System.Text.Json;
using Xunit;

namespace Dubai.Car.MarketPlace.Test.Services
{
    public class UaePassServiceTests
    {
        private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
        private readonly Mock<ILogger<UaePassService>> _loggerMock;
        private readonly UaePassSettings _uaePassSettings;
        private readonly UaePassService _uaePassService;

        public UaePassServiceTests()
        {
            _httpClientFactoryMock = new Mock<IHttpClientFactory>();
            _loggerMock = new Mock<ILogger<UaePassService>>();
            
            _uaePassSettings = new UaePassSettings
            {
                ClientId = "test-client-id",
                ClientSecret = "test-client-secret",
                AuthorizationEndpoint = "https://test.uaepass.ae/authorize",
                TokenEndpoint = "https://test.uaepass.ae/token",
                UserInfoEndpoint = "https://test.uaepass.ae/userinfo",
                RedirectUri = "https://test.app.com/callback",
                Scope = "urn:uae:digitalid:profile:general",
                IsSandbox = true,
                TimeoutSeconds = 30
            };

            var optionsMock = new Mock<IOptions<UaePassSettings>>();
            optionsMock.Setup(x => x.Value).Returns(_uaePassSettings);

            _uaePassService = new UaePassService(
                optionsMock.Object,
                _httpClientFactoryMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task GenerateAuthorizationUrlAsync_ShouldReturnValidUrl()
        {
            // Act
            var result = await _uaePassService.GenerateAuthorizationUrlAsync();

            // Assert
            Assert.True(result.IsSuccessful);
            Assert.NotNull(result.Data);
            Assert.Contains(_uaePassSettings.AuthorizationEndpoint, result.Data.AuthorizationUrl);
            Assert.Contains(_uaePassSettings.ClientId, result.Data.AuthorizationUrl);
            Assert.Contains(_uaePassSettings.RedirectUri, result.Data.AuthorizationUrl);
            Assert.NotEmpty(result.Data.State);
        }

        [Fact]
        public void GenerateState_ShouldReturnUniqueValues()
        {
            // Act
            var state1 = _uaePassService.GenerateState();
            var state2 = _uaePassService.GenerateState();

            // Assert
            Assert.NotEqual(state1, state2);
            Assert.NotEmpty(state1);
            Assert.NotEmpty(state2);
        }

        [Fact]
        public void ValidateState_WithValidState_ShouldReturnTrue()
        {
            // Arrange
            var state = _uaePassService.GenerateState();

            // Act
            var result = _uaePassService.ValidateState(state);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateState_WithInvalidState_ShouldReturnFalse()
        {
            // Act
            var result = _uaePassService.ValidateState("invalid-state");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateState_WithNullState_ShouldReturnFalse()
        {
            // Act
            var result = _uaePassService.ValidateState(null);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateState_WithEmptyState_ShouldReturnFalse()
        {
            // Act
            var result = _uaePassService.ValidateState(string.Empty);

            // Assert
            Assert.False(result);
        }
    }
}
