﻿namespace Dubai.Car.MarketPlace.Common.Configurations
{
    public class CacheConfiguration
    {

        /// <summary>
        /// Whether to enable caching using Redis. If false, no caching will be used.
        /// </summary>
        public bool EnableCaching { get; set; } = true;

        /// <summary>
        /// Default cache expiration time in minutes
        /// </summary>
        public int DefaultExpirationMinutes { get; set; } = 30;

        /// <summary>
        /// Maximum size of cache in MB
        /// </summary>
        public int MaxCacheSizeMB { get; set; } = 100;

        /// <summary>
        /// Cache eviction percentage when max size is reached
        /// </summary>
        public int EvictionPercentage { get; set; } = 25;

        /// <summary>
        /// Whether to enable cache warming
        /// </summary>
        public bool EnableCacheWarming { get; set; } = true;

        /// <summary>
        /// Interval in minutes for cache warming
        /// </summary>
        public int CacheWarmingIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// Number of days of payment data to pre-warm
        /// </summary>
        public int PaymentDataPrewarmDays { get; set; } = 7;
    }
}
