using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a car listing in the system
    /// </summary>
    public class Car : BaseEntity
    {
        /// <summary>
        /// The model name of the car
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Model { get; set; } = default!;

        /// <summary>
        /// The manufacturing year of the car
        /// </summary>
        [Required]
        public int Year { get; set; }

        /// <summary>
        /// The price of the car in AED
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        /// <summary>
        /// The mileage/odometer reading in kilometers
        /// </summary>
        public int? Mileage { get; set; }

        /// <summary>
        /// The color of the car
        /// </summary>
        [MaxLength(50)]
        public string? Color { get; set; }

        /// <summary>
        /// The VIN (Vehicle Identification Number)
        /// </summary>
        [MaxLength(17)]
        public string? VIN { get; set; }

        /// <summary>
        /// The license plate number
        /// </summary>
        [MaxLength(20)]
        public string? LicensePlate { get; set; }

        /// <summary>
        /// The engine size in liters
        /// </summary>
        [Column(TypeName = "decimal(3,1)")]
        public decimal? EngineSize { get; set; }

        /// <summary>
        /// The transmission type
        /// </summary>
        public TransmissionType? Transmission { get; set; }

        /// <summary>
        /// The number of doors
        /// </summary>
        public int? NumberOfDoors { get; set; }

        /// <summary>
        /// The number of seats
        /// </summary>
        public int? NumberOfSeats { get; set; }

        /// <summary>
        /// The condition of the car
        /// </summary>
        [Required]
        public CarCondition Condition { get; set; }

        /// <summary>
        /// The current status of the car listing
        /// </summary>
        [Required]
        public CarStatus Status { get; set; } = CarStatus.Draft;

        /// <summary>
        /// Description of the car
        /// </summary>
        [MaxLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Features of the car (JSON array)
        /// </summary>
        public string? Features { get; set; }

        /// <summary>
        /// Image keys for car photos (JSON array)
        /// </summary>
        public string? ImageKeys { get; set; }

        /// <summary>
        /// The main/primary image key
        /// </summary>
        public string? PrimaryImageKey { get; set; }

        /// <summary>
        /// Whether the car is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// Whether the car is sold
        /// </summary>
        public bool IsSold { get; set; } = false;

        /// <summary>
        /// The date when the car was sold
        /// </summary>
        public DateTime? SoldDate { get; set; }

        /// <summary>
        /// Number of views this car listing has received
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// Number of inquiries this car listing has received
        /// </summary>
        public int InquiryCount { get; set; } = 0;

        /// <summary>
        /// The date when the listing expires
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        // Foreign Keys
        /// <summary>
        /// The ID of the brand
        /// </summary>
        public Guid BrandId { get; set; }

        /// <summary>
        /// The ID of the body type
        /// </summary>
        public Guid BodyTypeId { get; set; }

        /// <summary>
        /// The ID of the fuel type
        /// </summary>
        public Guid FuelTypeId { get; set; }

        /// <summary>
        /// The ID of the vendor (optional for individual sellers)
        /// </summary>
        public Guid? VendorId { get; set; }

        /// <summary>
        /// The ID of the user who listed the car
        /// </summary>
        public Guid UserId { get; set; }

        // Navigation properties
        /// <summary>
        /// The brand of the car
        /// </summary>
        public virtual Brand Brand { get; set; } = default!;

        /// <summary>
        /// The body type of the car
        /// </summary>
        public virtual BodyType BodyType { get; set; } = default!;

        /// <summary>
        /// The fuel type of the car
        /// </summary>
        public virtual FuelType FuelType { get; set; } = default!;

        /// <summary>
        /// The vendor who listed the car (if applicable)
        /// </summary>
        public virtual Vendor? Vendor { get; set; }

        /// <summary>
        /// The user who listed the car
        /// </summary>
        public virtual User User { get; set; } = default!;

        /// <summary>
        /// Specification values for this car
        /// </summary>
        public virtual ICollection<CarSpecificationValue> SpecificationValues { get; set; } = new List<CarSpecificationValue>();
    }
}
