using AspNetCoreRateLimit;
using Dubai.Car.MarketPlace.Common.Configurations;
using RateLimitRule = AspNetCoreRateLimit.RateLimitRule;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Extension methods for configuring rate limiting services
    /// </summary>
    public static class RateLimitServiceExtension
    {
        /// <summary>
        /// Adds rate limiting services to the service collection
        /// </summary>
        /// <param name="services">The service collection to add services to</param>
        /// <param name="configuration">The application configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddRateLimitServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Load rate limit configuration
            services.Configure<RateLimitConfig>(configuration.GetSection("RateLimiting"));

            // Add memory cache to store rate limit counters
            services.AddMemoryCache();

            // Configure IP rate limiting middleware
            services.Configure<IpRateLimitOptions>(options =>
            {
                var config = configuration.GetSection("RateLimiting").Get<RateLimitConfig>();
                if (config == null)
                    throw new InvalidOperationException("RateLimiting configuration section is missing or invalid.");

                options.EnableEndpointRateLimiting = config.EnableEndpointRateLimiting;
                options.StackBlockedRequests = config.StackBlockedRequests;
                options.RealIpHeader = config.RealIpHeader;
                options.ClientIdHeader = config.ClientIdHeader;
                options.HttpStatusCode = config.HttpStatusCode;
                
                options.GeneralRules = config.GeneralRules.Select(rule => new RateLimitRule
                {
                    Endpoint = rule.Endpoint,
                    Limit = rule.Limit,
                    Period = rule.Period
                }).ToList();
            });

            // Configure IP rate limiting processing strategy
            services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
            services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
            services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
            services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();
            services.AddInMemoryRateLimiting();

            return services;
        }
    }
}
