### Admin Dashboard API Tests
### All requests require admin authentication

@baseUrl = https://localhost:7001/api/admin
@authToken = Bearer your-jwt-token-here

### Get complete dashboard data
GET {{baseUrl}}/dashboard
Authorization: {{authToken}}
Content-Type: application/json

###

### Get dashboard statistics only
GET {{baseUrl}}/dashboard/stats  
Authorization: {{authToken}}
Content-Type: application/json

###

### Get pending vendor applications (default 5)
GET {{baseUrl}}/dashboard/pending-vendor-applications
Authorization: {{authToken}}
Content-Type: application/json

###

### Get pending vendor applications (custom count)
GET {{baseUrl}}/dashboard/pending-vendor-applications?count=10
Authorization: {{authToken}}
Content-Type: application/json

###

### Get platform revenue data
GET {{baseUrl}}/dashboard/revenue
Authorization: {{authToken}}
Content-Type: application/json

###

### Test invalid count parameter (should return 400)
GET {{baseUrl}}/dashboard/pending-vendor-applications?count=100
Authorization: {{authToken}}
Content-Type: application/json

###
