using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Car
{
    /// <summary>
    /// Request DTO for creating a new car listing
    /// </summary>
    public class CreateCarRequest
    {
        /// <summary>
        /// The model name of the car
        /// </summary>
        [Required(ErrorMessage = "Model is required")]
        [StringLength(100, ErrorMessage = "Model cannot exceed 100 characters")]
        public string Model { get; set; } = default!;

        /// <summary>
        /// The manufacturing year of the car
        /// </summary>
        [Required(ErrorMessage = "Year is required")]
        [Range(1900, 2030, ErrorMessage = "Year must be between 1900 and 2030")]
        public int Year { get; set; }

        /// <summary>
        /// The price of the car in AED
        /// </summary>
        [Required(ErrorMessage = "Price is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal Price { get; set; }

        /// <summary>
        /// The mileage/odometer reading in kilometers
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Mileage cannot be negative")]
        public int? Mileage { get; set; }

        /// <summary>
        /// The color of the car
        /// </summary>
        [StringLength(50, ErrorMessage = "Color cannot exceed 50 characters")]
        public string? Color { get; set; }

        /// <summary>
        /// The VIN (Vehicle Identification Number)
        /// </summary>
        [StringLength(17, MinimumLength = 17, ErrorMessage = "VIN must be exactly 17 characters")]
        public string? VIN { get; set; }

        /// <summary>
        /// The license plate number
        /// </summary>
        [StringLength(20, ErrorMessage = "License plate cannot exceed 20 characters")]
        public string? LicensePlate { get; set; }

        /// <summary>
        /// The engine size in liters
        /// </summary>
        [Range(0.1, 20.0, ErrorMessage = "Engine size must be between 0.1 and 20.0 liters")]
        public decimal? EngineSize { get; set; }

        /// <summary>
        /// The transmission type
        /// </summary>
        public TransmissionType? Transmission { get; set; }

        /// <summary>
        /// The number of doors
        /// </summary>
        [Range(2, 6, ErrorMessage = "Number of doors must be between 2 and 6")]
        public int? NumberOfDoors { get; set; }

        /// <summary>
        /// The number of seats
        /// </summary>
        [Range(1, 12, ErrorMessage = "Number of seats must be between 1 and 12")]
        public int? NumberOfSeats { get; set; }

        /// <summary>
        /// The condition of the car
        /// </summary>
        [Required(ErrorMessage = "Condition is required")]
        public CarCondition Condition { get; set; }

        /// <summary>
        /// Description of the car
        /// </summary>
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Features of the car
        /// </summary>
        public List<string>? Features { get; set; }

        /// <summary>
        /// Whether the car is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// The date when the listing expires
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        // Foreign Keys
        /// <summary>
        /// The ID of the brand
        /// </summary>
        [Required(ErrorMessage = "Brand is required")]
        public Guid BrandId { get; set; }

        /// <summary>
        /// The ID of the body type
        /// </summary>
        [Required(ErrorMessage = "Body type is required")]
        public Guid BodyTypeId { get; set; }

        /// <summary>
        /// The ID of the fuel type
        /// </summary>
        [Required(ErrorMessage = "Fuel type is required")]
        public Guid FuelTypeId { get; set; }

        /// <summary>
        /// The ID of the vendor (optional for individual sellers)
        /// </summary>
        public Guid? VendorId { get; set; }

        /// <summary>
        /// Specification values for this car
        /// </summary>
        public List<CreateCarSpecificationValueRequest>? SpecificationValues { get; set; }

        /// <summary>
        /// Optional car images to upload
        /// </summary>
        public List<IFormFile>? Images { get; set; }

        /// <summary>
        /// Index of the primary image in the Images list (0-based)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Primary image index must be 0 or greater")]
        public int? PrimaryImageIndex { get; set; }
    }

    /// <summary>
    /// Request DTO for creating a car specification value
    /// </summary>
    public class CreateCarSpecificationValueRequest
    {
        /// <summary>
        /// The ID of the specification template
        /// </summary>
        [Required(ErrorMessage = "Specification ID is required")]
        public Guid CarSpecificationId { get; set; }

        /// <summary>
        /// The value of the specification
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        [StringLength(500, ErrorMessage = "Value cannot exceed 500 characters")]
        public string Value { get; set; } = default!;

        /// <summary>
        /// Additional notes or comments about this specification value
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }
    }
}
