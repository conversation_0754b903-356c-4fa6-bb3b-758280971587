using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin
{
    /// <summary>
    /// Request DTO for assigning or removing permissions from a role
    /// </summary>
    public class AssignRolePermissionsRequestDto
    {
        /// <summary>
        /// Role ID to assign permissions to
        /// </summary>
        [Required]
        public Guid RoleId { get; set; }

        /// <summary>
        /// List of permission IDs to assign to the role
        /// </summary>
        [Required]
        public List<int> PermissionIds { get; set; } = new();
    }
}
