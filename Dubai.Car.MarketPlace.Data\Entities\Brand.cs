using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a car brand/make in the system
    /// </summary>
    public class Brand : BaseEntity
    {
        /// <summary>
        /// The name of the brand (e.g., BMW, Mercedes-Benz, Toyota)
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = default!;

        /// <summary>
        /// The description of the brand
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// The logo image key for storage (S3, etc.)
        /// </summary>
        public string? LogoImageKey { get; set; }

        /// <summary>
        /// The country of origin for the brand
        /// </summary>
        [MaxLength(100)]
        public string? CountryOfOrigin { get; set; }

        /// <summary>
        /// Whether this brand is currently active for new car listings
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting brands in UI
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        // Navigation properties
        /// <summary>
        /// Cars associated with this brand
        /// </summary>
        public virtual ICollection<Car> Cars { get; set; } = new List<Car>();
    }
}
