using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    /// <summary>
    /// Seeder for creating admin users with appropriate roles
    /// </summary>
    public static class AdminUserSeeder
    {
        public static async Task SeedAdminUsersAsync(IServiceProvider serviceProvider, ILogger logger)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<CarContext>();
                var userManager = scope.ServiceProvider.GetRequiredService<UserManager<User>>();
                var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<Role>>();

                // Check if admin users already exist
                var existingAdmins = await userManager.Users
                    .Where(u => u.UserType == AuthEnums.UserTypes.Admin)
                    .ToListAsync();

                if (existingAdmins.Any())
                {
                    logger.LogInformation("Admin users already exist. Skipping admin user seeding.");
                    return;
                }

                // Ensure admin roles exist
                await EnsureRolesExistAsync(roleManager, logger);

                // Sample admin users with different roles
                var adminUsers = new List<(User user, string password, string roleName)>
                {
                    // Super Admin User
                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "System",
                        LastName = "Administrator",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Male",
                        UserType = AuthEnums.UserTypes.Admin,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Dubai Internet City, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow,
                        IsProfileCompleted = true
                    }, "SuperAdmin123!", "Super Admin"),

                    // Content Admin User
                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Content",
                        LastName = "Manager",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Female",
                        UserType = AuthEnums.UserTypes.Admin,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Business Bay, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow,
                        IsProfileCompleted = true
                    }, "ContentAdmin123!", "Content Admin"),

                    // Support Admin User
                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Support",
                        LastName = "Representative",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Male",
                        UserType = AuthEnums.UserTypes.Admin,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Dubai Marina, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow,
                        IsProfileCompleted = true
                    }, "SupportAdmin123!", "Support Admin"),

                    // Financial Admin User
                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Financial",
                        LastName = "Controller",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Female",
                        UserType = AuthEnums.UserTypes.Admin,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "DIFC, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow,
                        IsProfileCompleted = true
                    }, "FinancialAdmin123!", "Financial Admin"),

                    // Additional Super Admin for redundancy
                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Admin",
                        LastName = "Backup",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Male",
                        UserType = AuthEnums.UserTypes.Admin,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Downtown Dubai, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow,
                        IsProfileCompleted = true
                    }, "AdminBackup123!", "Super Admin")
                };

                // Create admin users
                foreach (var (user, password, roleName) in adminUsers)
                {
                    try
                    {
                        // Check if user already exists
                        var existingUser = await userManager.FindByEmailAsync(user.Email!);
                        if (existingUser != null)
                        {
                            logger.LogInformation($"Admin user {user.Email} already exists. Skipping.");
                            continue;
                        }

                        // Create the user
                        var result = await userManager.CreateAsync(user, password);
                        if (!result.Succeeded)
                        {
                            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                            logger.LogError($"Failed to create admin user {user.Email}: {errors}");
                            continue;
                        }

                        // Assign role to user
                        var roleResult = await userManager.AddToRoleAsync(user, roleName);
                        if (!roleResult.Succeeded)
                        {
                            var roleErrors = string.Join(", ", roleResult.Errors.Select(e => e.Description));
                            logger.LogError($"Failed to assign role {roleName} to user {user.Email}: {roleErrors}");
                        }
                        else
                        {
                            logger.LogInformation($"Admin user {user.Email} created successfully with role {roleName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, $"Error creating admin user {user.Email}");
                    }
                }

                logger.LogInformation("Admin user seeding completed.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred during admin user seeding.");
                throw;
            }
        }

        /// <summary>
        /// Ensures that admin roles exist in the system
        /// </summary>
        /// <param name="roleManager">Role manager instance</param>
        /// <param name="logger">Logger instance</param>
        private static async Task EnsureRolesExistAsync(RoleManager<Role> roleManager, ILogger logger)
        {
            var requiredRoles = new[]
            {
                "Super Admin",
                "Content Admin", 
                "Support Admin",
                "Financial Admin"
            };

            foreach (var roleName in requiredRoles)
            {
                var role = await roleManager.FindByNameAsync(roleName);
                if (role == null)
                {
                    logger.LogWarning($"Role {roleName} not found. This role should be created by RoleSeeder first.");
                }
            }
        }
    }
}
