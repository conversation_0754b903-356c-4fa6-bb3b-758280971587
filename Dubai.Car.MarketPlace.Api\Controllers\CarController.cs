using Dubai.Car.MarketPlace.Api.Attributes;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// Controller for car management operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CarController : BaseController
    {
        private readonly ICarService _carService;
        private readonly ICarLookupService _carLookupService;

        /// <summary>
        /// Initializes a new instance of the <see cref="CarController"/> class with the specified services.
        /// </summary>
        /// <param name="carService">The service used to manage car-related operations.</param>
        /// <param name="carLookupService">The service used to perform car lookup operations.</param>
        public CarController(ICarService carService, ICarLookupService carLookupService)
        {
            _carService = carService;
            _carLookupService = carLookupService;
        }

        /// <summary>
        /// Gets cars with filtering and pagination
        /// </summary>
        /// <param name="request">Filter request</param>
        /// <returns>Paginated car listings</returns>
        [HttpGet("get-cars")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>>> GetCars([FromQuery] CarFilterRequest request)
        {
            var result = await _carService.GetCarsAsync(request);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets a car by ID
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <returns>Car details</returns>
        [HttpGet("get-car/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarResponseDto>>> GetCar(Guid id)
        {
            var userId = GetCurrentUserId();
            var result = await _carService.GetCarByIdAsync(id, userId);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a new car listing with optional images
        /// </summary>
        /// <param name="request">Car creation request</param>
        /// <returns>Created car details</returns>
        [HttpPost("create-car")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<CarResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarResponseDto>>> CreateCar([FromForm] CreateCarRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<CarResponseDto>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            if (!ModelState.IsValid)
            {
                var errors = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ApiResponse<CarResponseDto>.Failed($"Validation failed: {errors}", System.Net.HttpStatusCode.BadRequest));
            }

            var result = await _carService.CreateCarAsync(request, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates an existing car listing with optional images
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Car update request</param>
        /// <returns>Updated car details</returns>
        [HttpPut("update-car/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<CarResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarResponseDto>>> UpdateCar(Guid id, [FromForm] UpdateCarRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<CarResponseDto>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            if (!ModelState.IsValid)
            {
                var errors = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ApiResponse<CarResponseDto>.Failed($"Validation failed: {errors}", System.Net.HttpStatusCode.BadRequest));
            }

            var result = await _carService.UpdateCarAsync(id, request, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Deletes a car listing
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("delete-car/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteCar(Guid id)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            var result = await _carService.DeleteCarAsync(id, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Marks a car as sold
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <returns>Success response</returns>
        [HttpPost("mark-sold/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> MarkCarAsSold(Guid id)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            var result = await _carService.MarkCarAsSoldAsync(id, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Uploads car images to AWS S3 and sets car status based on user role
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Image upload request</param>
        /// <returns>Success response</returns>
        [HttpPost("upload-images/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> UploadCarImages(Guid id, [FromForm] UploadCarImagesRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<bool>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            if (!ModelState.IsValid)
            {
                var errors = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ApiResponse<bool>.Failed($"Validation failed: {errors}", System.Net.HttpStatusCode.BadRequest));
            }

            var result = await _carService.UploadCarImagesAsync(id, request.Images, request.PrimaryImageIndex, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates car images by replacing existing ones
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <param name="request">Image update request</param>
        /// <returns>Success response</returns>
        [HttpPost("update-images/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> UpdateCarImages(Guid id, [FromForm] UploadCarImagesRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<bool>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            if (!ModelState.IsValid)
            {
                var errors = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ApiResponse<bool>.Failed($"Validation failed: {errors}", System.Net.HttpStatusCode.BadRequest));
            }

            var result = await _carService.UpdateCarImagesAsync(id, request.Images, request.PrimaryImageIndex, userId.Value);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Increments the view count for a car
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <returns>Success response</returns>
        [HttpPost("increament-view-count")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> IncrementViewCount(Guid id)
        {
            var result = await _carService.IncrementViewCountAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Increments the inquiry count for a car
        /// </summary>
        /// <param name="id">Car ID</param>
        /// <returns>Success response</returns>
        [HttpPost("inquiry/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> IncrementInquiryCount(Guid id)
        {
            var result = await _carService.IncrementInquiryCountAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets featured cars
        /// </summary>
        /// <param name="count">Number of featured cars to return</param>
        /// <returns>Featured cars</returns>
        [HttpGet("get-featured")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<CarSummaryResponseDto>>>> GetFeaturedCars([FromQuery] int count = 10)
        {
            var result = await _carService.GetFeaturedCarsAsync(count);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets recently added cars
        /// </summary>
        /// <param name="count">Number of recent cars to return</param>
        /// <returns>Recent cars</returns>
        [HttpGet("get-recent")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<CarSummaryResponseDto>>>> GetRecentCars([FromQuery] int count = 10)
        {
            var result = await _carService.GetRecentCarsAsync(count);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets car statistics
        /// </summary>
        /// <returns>Car statistics</returns>
        [HttpGet("get-statistics")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarStatisticsResponseDto>>> GetCarStatistics()
        {
            var userId = GetCurrentUserId();
            var result = await _carService.GetCarStatisticsAsync(userId);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets cars for the current user
        /// </summary>
        /// <param name="request">Filter request</param>
        /// <returns>User's cars</returns>
        [HttpGet("get-my-cars")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>>> GetMyCars([FromQuery] CarFilterRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>
                    .Failed("User not authenticated", System.Net.HttpStatusCode.Unauthorized));
            }

            var result = await _carService.GetUserCarsAsync(userId.Value, request);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets all brands
        /// </summary>
        /// <returns>List of brands</returns>
        [HttpGet("get-brands")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<BrandResponseDto>>>> GetBrands()
        {
            var result = await _carLookupService.GetBrandsAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets all body types
        /// </summary>
        /// <returns>List of body types</returns>
        [HttpGet("get-body-types")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<BodyTypeResponseDto>>>> GetBodyTypes()
        {
            var result = await _carLookupService.GetBodyTypesAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets all fuel types
        /// </summary>
        /// <returns>List of fuel types</returns>
        [HttpGet("get-fuel-types")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<FuelTypeResponseDto>>>> GetFuelTypes()
        {
            var result = await _carLookupService.GetFuelTypesAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }
    }

}
