@Dubai.Car.MarketPlace.Api_HostAddress = http://localhost:5094
@contentType = application/json

### 1. Get UAE PASS Authorization URL
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/uaepass/start
Accept: application/json

###

### 2. Handle UAE PASS Callback (POST)
POST {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/uaepass/callback
Content-Type: {{contentType}}

{
  "code": "sample-authorization-code",
  "state": "sample-state-parameter",
  "error": null,
  "errorDescription": null
}

###

### 3. Handle UAE PASS Callback (GET) - Success
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/uaepass/callback?code=sample-authorization-code&state=sample-state-parameter
Accept: application/json

###

### 4. Handle UAE PASS Callback (GET) - Error
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/uaepass/callback?error=access_denied&error_description=User%20denied%20access
Accept: application/json

###

### 5. Test with Missing Code
POST {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/uaepass/callback
Content-Type: {{contentType}}

{
  "code": "",
  "state": "sample-state-parameter"
}

###

### 6. Test with Error Response
POST {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/uaepass/callback
Content-Type: {{contentType}}

{
  "code": "",
  "state": "sample-state-parameter",
  "error": "access_denied",
  "errorDescription": "User denied access to the application"
}

###
