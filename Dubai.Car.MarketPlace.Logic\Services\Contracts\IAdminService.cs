using Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Interface for admin-related operations
    /// </summary>
    public interface IAdminService
    {
        /// <summary>
        /// Creates a new admin user
        /// </summary>
        /// <param name="model">Create admin user request</param>
        /// <returns>Created admin user information</returns>
        Task<ApiResponse<AdminUserResponseDto>> CreateAdminUserAsync(CreateAdminUserRequestDto model);

        /// <summary>
        /// Updates an existing admin user
        /// </summary>
        /// <param name="userId">User ID to update</param>
        /// <param name="model">Update admin user request</param>
        /// <returns>Updated admin user information</returns>
        Task<ApiResponse<AdminUserResponseDto>> UpdateAdminUserAsync(Guid userId, UpdateAdminUserRequestDto model);

        /// <summary>
        /// Deletes an admin user
        /// </summary>
        /// <param name="userId">User ID to delete</param>
        /// <returns>Delete operation result</returns>
        Task<ApiResponse<bool>> DeleteAdminUserAsync(Guid userId);

        /// <summary>
        /// Gets an admin user by ID
        /// </summary>
        /// <param name="userId">User ID to retrieve</param>
        /// <returns>Admin user information</returns>
        Task<ApiResponse<AdminUserResponseDto>> GetAdminUserByIdAsync(Guid userId);

        /// <summary>
        /// Gets all admin users with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>List of admin users</returns>
        Task<ApiResponse<List<AdminUserResponseDto>>> GetAllAdminUsersAsync(int page = 1, int pageSize = 10);

        /// <summary>
        /// Locks or unlocks an admin user
        /// </summary>
        /// <param name="userId">User ID to lock/unlock</param>
        /// <param name="model">Lock admin user request</param>
        /// <returns>Lock operation result</returns>
        Task<ApiResponse<bool>> LockUnlockAdminUserAsync(Guid userId, LockAdminUserRequestDto model);

        /// <summary>
        /// Gets all available permissions
        /// </summary>
        /// <returns>List of available permissions</returns>
        Task<ApiResponse<List<PermissionResponseDto>>> GetAllPermissionsAsync();

        /// <summary>
        /// Assigns or removes permissions from a role
        /// </summary>
        /// <param name="model">Assign role permissions request</param>
        /// <returns>Assignment operation result</returns>
        Task<ApiResponse<bool>> AssignRolePermissionsAsync(AssignRolePermissionsRequestDto model);

        /// <summary>
        /// Gets all roles with their permissions
        /// </summary>
        /// <returns>List of roles with permissions</returns>
        Task<ApiResponse<List<RoleWithPermissionsResponseDto>>> GetAllRolesWithPermissionsAsync();

        /// <summary>
        /// Gets a role by ID with its permissions
        /// </summary>
        /// <param name="roleId">Role ID to retrieve</param>
        /// <returns>Role with permissions</returns>
        Task<ApiResponse<RoleWithPermissionsResponseDto>> GetRoleByIdWithPermissionsAsync(Guid roleId);

        /// <summary>
        /// Gets dashboard statistics and data
        /// </summary>
        /// <returns>Dashboard data</returns>
        Task<ApiResponse<DashboardResponseDto>> GetDashboardDataAsync();

        /// <summary>
        /// Gets dashboard statistics only
        /// </summary>
        /// <returns>Dashboard statistics</returns>
        Task<ApiResponse<DashboardStatsResponseDto>> GetDashboardStatsAsync();

        /// <summary>
        /// Gets pending vendor applications for dashboard
        /// </summary>
        /// <param name="count">Number of applications to return (default: 5)</param>
        /// <returns>Pending vendor applications</returns>
        Task<ApiResponse<List<DashboardVendorApplicationResponseDto>>> GetPendingVendorApplicationsAsync(int count = 5);

        /// <summary>
        /// Gets platform revenue data for dashboard
        /// </summary>
        /// <returns>Platform revenue data</returns>
        Task<ApiResponse<DashboardRevenueResponseDto>> GetPlatformRevenueDataAsync();
    }
}
