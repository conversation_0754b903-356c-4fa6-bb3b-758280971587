namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for admin user information
    /// </summary>
    public class AdminUserResponseDto
    {
        /// <summary>
        /// User ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Full name of the admin user
        /// </summary>
        public string FullName { get; set; } = default!;

        /// <summary>
        /// First name of the admin user
        /// </summary>
        public string FirstName { get; set; } = default!;

        /// <summary>
        /// Last name of the admin user
        /// </summary>
        public string LastName { get; set; } = default!;

        /// <summary>
        /// Email address of the admin user
        /// </summary>
        public string Email { get; set; } = default!;

        /// <summary>
        /// Phone number of the admin user
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Role name of the admin user
        /// </summary>
        public string Role { get; set; } = default!;

        /// <summary>
        /// Role ID of the admin user
        /// </summary>
        public Guid RoleId { get; set; }

        /// <summary>
        /// Whether the admin user is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether the admin user is locked
        /// </summary>
        public bool IsLocked { get; set; }

        /// <summary>
        /// Lock end date if the user is locked
        /// </summary>
        public DateTimeOffset? LockoutEnd { get; set; }

        /// <summary>
        /// Gender of the admin user
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// Country of the admin user
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// State of the admin user
        /// </summary>
        public string? State { get; set; }

        /// <summary>
        /// Date when the admin user was created
        /// </summary>
        public DateTime? CreatedOn { get; set; }

        /// <summary>
        /// Date when the admin user was last updated
        /// </summary>
        public DateTime? UpdatedOn { get; set; }

        /// <summary>
        /// Whether the admin user's email is confirmed
        /// </summary>
        public bool EmailConfirmed { get; set; }

        /// <summary>
        /// Whether the admin user's phone number is confirmed
        /// </summary>
        public bool PhoneNumberConfirmed { get; set; }
    }
}
