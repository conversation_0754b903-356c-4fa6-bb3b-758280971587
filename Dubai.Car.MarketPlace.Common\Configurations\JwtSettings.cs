namespace Dubai.Car.MarketPlace.Common.Configurations;

public class JwtSettings
{
    public string SecretKey { get; set; } = string.Empty;
    public string Issuer { get; set; } = string.Empty;
    public int Lifetime { get; set; }
    public string Audience { get; set; } = string.Empty;
}

public class UaePassSettings
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string AuthorizationEndpoint { get; set; } = string.Empty;
    public string TokenEndpoint { get; set; } = string.Empty;
    public string UserInfoEndpoint { get; set; } = string.Empty;
    public string RedirectUri { get; set; } = string.Empty;
    public string Scope { get; set; } = "urn:uae:digitalid:profile:general";
    public bool IsSandbox { get; set; } = true;
    public int TimeoutSeconds { get; set; } = 30;
}