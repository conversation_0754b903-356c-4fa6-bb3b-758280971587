﻿using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth
{
    public class RefreshTokenRequestDto
    {
        [Required(ErrorMessage = "Access token is required")]
        public string AccessToken { get; set; } = string.Empty;

        [Required(ErrorMessage = "Refresh token is required")]
        public string RefreshToken { get; set; } = string.Empty;
    }
}
