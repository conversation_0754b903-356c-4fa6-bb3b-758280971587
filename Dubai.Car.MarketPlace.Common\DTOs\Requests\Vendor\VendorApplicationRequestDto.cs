using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Vendor
{
    /// <summary>
    /// DTO for vendor application request
    /// </summary>
    public class VendorApplicationRequestDto
    {
        /// <summary>
        /// The business name of the vendor
        /// </summary>
        [Required(ErrorMessage = "Business name is required")]
        [StringLength(100, ErrorMessage = "Business name cannot exceed 100 characters")]
        public string BusinessName { get; set; } = default!;
        
        /// <summary>
        /// The contact person's name
        /// </summary>
        [Required(ErrorMessage = "Contact person name is required")]
        [StringLength(100, ErrorMessage = "Contact person name cannot exceed 100 characters")]
        public string ContactPersonName { get; set; } = default!;
        
        /// <summary>
        /// The position of the contact person
        /// </summary>
        [Required(ErrorMessage = "Position is required")]
        [StringLength(100, ErrorMessage = "Position cannot exceed 100 characters")]
        public string Position { get; set; } = default!;
        
        /// <summary>
        /// The business email
        /// </summary>
        [Required(ErrorMessage = "Business email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string BusinessEmail { get; set; } = default!;
        
        /// <summary>
        /// The phone number
        /// </summary>
        [Required(ErrorMessage = "Phone number is required")]
        [RegularExpression(@"^\+?[0-9]{10,15}$", ErrorMessage = "Invalid phone number format")]
        public string PhoneNumber { get; set; } = default!;
        
        /// <summary>
        /// The business address
        /// </summary>
        [Required(ErrorMessage = "Business address is required")]
        public string BusinessAddress { get; set; } = default!;
        
        /// <summary>
        /// The trade license number
        /// </summary>
        [Required(ErrorMessage = "Trade license number is required")]
        public string TradeLicenseNumber { get; set; } = default!;
        
        /// <summary>
        /// The city where the business is located
        /// </summary>
        [Required(ErrorMessage = "City is required")]
        public string City { get; set; } = default!;
        
        /// <summary>
        /// The business specialization (car brands)
        /// </summary>
        public string? Specialization { get; set; }
        
        /// <summary>
        /// The business description
        /// </summary>
        public string? BusinessDescription { get; set; }
        
        /// <summary>
        /// Trade license document as base64
        /// </summary>
        [Required(ErrorMessage = "Trade license document is required")]
        public string TradeLicenseDocumentBase64 { get; set; } = default!;
        
        /// <summary>
        /// Trade license document filename
        /// </summary>
        [Required(ErrorMessage = "Trade license document filename is required")]
        public string TradeLicenseDocumentFilename { get; set; } = default!;
        
        /// <summary>
        /// Additional documents as base64 (optional)
        /// </summary>
        public List<DocumentDto>? AdditionalDocuments { get; set; }
    }
    
    /// <summary>
    /// DTO for document upload
    /// </summary>
    public class DocumentDto
    {
        /// <summary>
        /// Document as base64 string
        /// </summary>
        [Required]
        public string Base64Content { get; set; } = default!;
        
        /// <summary>
        /// Document filename
        /// </summary>
        [Required]
        public string Filename { get; set; } = default!;
        
        /// <summary>
        /// Document description
        /// </summary>
        public string? Description { get; set; }
    }
}
