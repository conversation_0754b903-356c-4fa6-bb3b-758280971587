using System.ComponentModel;

namespace Dubai.Car.MarketPlace.Common.Enums
{
    public class RoleEnums
    {
        public enum SystemRoles
        {
            [Description("Super Admin")]
            SuperAdmin = 1,

            [Description("Content Admin")]
            ContentAdmin = 2,

            [Description("Support Admin")]
            SupportAdmin = 3,

            [Description("Financial Admin")]
            FinancialAdmin = 4,

            [Description("Customer")]
            Customer = 5,

            [Description("Private Seller")]
            PrivateSeller = 6,

            [Description("Vendor")]
            Vendor = 7
        }

        public enum SystemPermissions
        {
            // User Management
            [Description("User Management")]
            UserManagement = 1,

            // Vendor Management
            [Description("Vendor Management")]
            VendorManagement = 2,

            // Content Management
            [Description("Content Management")]
            ContentManagement = 3,

            [Description("Blog Management")]
            BlogManagement = 4,

            [Description("Review Moderation")]
            ReviewModeration = 5,

            [Description("Advertisement Management")]
            AdvertisementManagement = 6,

            // Financial Management
            [Description("Financial Management")]
            FinancialManagement = 7,

            [Description("Payment Management")]
            PaymentManagement = 16,

            [Description("Subscription Oversight")]
            SubscriptionOversight = 17,

            [Description("Financial Reports")]
            FinancialReports = 18,

            // System Settings
            [Description("System Settings")]
            SystemSettings = 19,

            [Description("Admin Management")]
            AdminManagement = 20,

            // Support Management
            [Description("Support Tickets")]
            SupportTickets = 21,

            [Description("User Communication")]
            UserCommunication = 22,

            [Description("Basic User Management")]
            BasicUserManagement = 23,

            [Description("Report Generation")]
            ReportGenerate = 30,
        }
    }
}
