namespace Dubai.Car.MarketPlace.Common.Configurations
{
    /// <summary>
    /// Configuration settings for API rate limiting
    /// </summary>
    public class RateLimitConfig
    {
        /// <summary>
        /// Gets or sets a value indicating whether endpoint rate limiting is enabled
        /// When true, rate limits are applied to each endpoint separately
        /// </summary>
        public bool EnableEndpointRateLimiting { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether blocked requests should be stacked
        /// When false, blocked requests are discarded
        /// </summary>
        public bool StackBlockedRequests { get; set; }

        /// <summary>
        /// Gets or sets the header name used to identify the real client IP address
        /// Default is "X-Real-IP"
        /// </summary>
        public string RealIpHeader { get; set; } = "X-Real-IP";

        /// <summary>
        /// Gets or sets the header name used to identify the client ID
        /// Default is "X-ClientId"
        /// </summary>
        public string ClientIdHeader { get; set; } = "X-ClientId";

        /// <summary>
        /// Gets or sets the HTTP status code returned when rate limit is exceeded
        /// Default is 429 (Too Many Requests)
        /// </summary>
        public int HttpStatusCode { get; set; } = 429;

        /// <summary>
        /// Gets or sets the general rules for rate limiting
        /// </summary>
        public List<RateLimitRule> GeneralRules { get; set; } = new();
    }    /// <summary>
    /// Defines a rule for rate limiting specific endpoints
    /// </summary>
    public class RateLimitRule
    {
        /// <summary>
        /// Gets or sets the endpoint pattern this rule applies to
        /// Can include wildcards, e.g., "get:/api/*"
        /// </summary>
        public required string Endpoint { get; set; }

        /// <summary>
        /// Gets or sets the time period for the rate limit
        /// Format examples: "1s", "5m", "1h", "1d"
        /// </summary>
        public required string Period { get; set; }

        /// <summary>
        /// Gets or sets the maximum number of requests allowed within the period
        /// </summary>
        public int Limit { get; set; }
    }
}
