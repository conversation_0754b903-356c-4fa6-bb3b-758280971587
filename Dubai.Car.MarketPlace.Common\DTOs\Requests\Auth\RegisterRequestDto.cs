﻿using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth
{
    public class RegisterRequestDto
    {
        [Required(ErrorMessage = "First name is required")]
        private string _firstName { get; set; } = default!;
        public string FirstName
        {
            get => _firstName;
            set => _firstName = char.ToUpper(value[0]) + value.Substring(1).Trim();
        }

        [Required(ErrorMessage = "Last name is required")]
        private string _lastName { get; set; } = default!;
        public string LastName
        {
            get => _lastName;
            set => _lastName = char.ToUpper(value[0]) + value.Substring(1).Trim();
        }

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email address")]
        private string _email { get; set; } = default!;
        public string Email
        {
            get => _email;
            set => _email = value.ToLower();
        }

        [DataType(DataType.Password)]
        [Required(ErrorMessage = "Password is required")]
        [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Required(ErrorMessage = "Confirm password is required")]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
