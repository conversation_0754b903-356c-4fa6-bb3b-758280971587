using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Car
{
    /// <summary>
    /// Response DTO for car specification template
    /// </summary>
    public class CarSpecificationResponseDto
    {
        /// <summary>
        /// The unique ID of the specification
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The name of the specification
        /// </summary>
        public string Name { get; set; } = default!;

        /// <summary>
        /// The category of the specification for grouping
        /// </summary>
        public SpecificationCategory Category { get; set; }

        /// <summary>
        /// The category name for display
        /// </summary>
        public string CategoryName { get; set; } = default!;

        /// <summary>
        /// The data type of the specification value
        /// </summary>
        public SpecificationDataType DataType { get; set; }

        /// <summary>
        /// The data type name for display
        /// </summary>
        public string DataTypeName { get; set; } = default!;

        /// <summary>
        /// The unit of measurement
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// The description of the specification
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Whether this specification is required for car listings
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Whether this specification is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Display order for sorting specifications in UI
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Predefined options for dropdown/select specifications
        /// </summary>
        public List<string>? PredefinedOptions { get; set; }

        /// <summary>
        /// Minimum value for numeric specifications
        /// </summary>
        public decimal? MinValue { get; set; }

        /// <summary>
        /// Maximum value for numeric specifications
        /// </summary>
        public decimal? MaxValue { get; set; }

        /// <summary>
        /// Regular expression pattern for text validation
        /// </summary>
        public string? ValidationPattern { get; set; }

        /// <summary>
        /// Help text to display to users
        /// </summary>
        public string? HelpText { get; set; }

        /// <summary>
        /// The date when the specification was created
        /// </summary>
        public DateTime CreatedOn { get; set; }

        /// <summary>
        /// The date when the specification was last modified
        /// </summary>
        public DateTime? ModifiedOn { get; set; }
    }

    /// <summary>
    /// Response DTO for car specification value
    /// </summary>
    public class CarSpecificationValueResponseDto
    {
        /// <summary>
        /// The unique ID of the specification value
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The value of the specification
        /// </summary>
        public string Value { get; set; } = default!;

        /// <summary>
        /// Additional notes or comments about this specification value
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// The specification template
        /// </summary>
        public CarSpecificationResponseDto CarSpecification { get; set; } = default!;

        /// <summary>
        /// The date when the specification value was created
        /// </summary>
        public DateTime CreatedOn { get; set; }

        /// <summary>
        /// The date when the specification value was last modified
        /// </summary>
        public DateTime? ModifiedOn { get; set; }
    }

    /// <summary>
    /// Grouped response DTO for car specifications by category
    /// </summary>
    public class GroupedCarSpecificationResponseDto
    {
        /// <summary>
        /// The specification category
        /// </summary>
        public SpecificationCategory Category { get; set; }

        /// <summary>
        /// The category name for display
        /// </summary>
        public string CategoryName { get; set; } = default!;

        /// <summary>
        /// The specifications in this category
        /// </summary>
        public List<CarSpecificationResponseDto> Specifications { get; set; } = new List<CarSpecificationResponseDto>();
    }

    /// <summary>
    /// Grouped response DTO for car specification values by category
    /// </summary>
    public class GroupedCarSpecificationValueResponseDto
    {
        /// <summary>
        /// The specification category
        /// </summary>
        public SpecificationCategory Category { get; set; }

        /// <summary>
        /// The category name for display
        /// </summary>
        public string CategoryName { get; set; } = default!;

        /// <summary>
        /// The specification values in this category
        /// </summary>
        public List<CarSpecificationValueResponseDto> SpecificationValues { get; set; } = new List<CarSpecificationValueResponseDto>();
    }
}
