using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Microsoft.AspNetCore.Http;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Service contract for car management operations
    /// </summary>
    public interface ICarService
    {
        /// <summary>
        /// Creates a new car listing
        /// </summary>
        /// <param name="request">The car creation request</param>
        /// <param name="userId">The ID of the user creating the car</param>
        /// <returns>The created car response</returns>
        Task<ApiResponse<CarResponseDto>> CreateCarAsync(CreateCarRequest request, Guid userId);

        /// <summary>
        /// Updates an existing car listing
        /// </summary>
        /// <param name="carId">The ID of the car to update</param>
        /// <param name="request">The car update request</param>
        /// <param name="userId">The ID of the user updating the car</param>
        /// <returns>The updated car response</returns>
        Task<ApiResponse<CarResponseDto>> UpdateCarAsync(Guid carId, UpdateCarRequest request, Guid userId);

        /// <summary>
        /// Gets a car by ID
        /// </summary>
        /// <param name="carId">The ID of the car</param>
        /// <param name="userId">The ID of the requesting user (for view tracking)</param>
        /// <returns>The car response</returns>
        Task<ApiResponse<CarResponseDto>> GetCarByIdAsync(Guid carId, Guid? userId = null);

        /// <summary>
        /// Gets cars with filtering and pagination
        /// </summary>
        /// <param name="request">The filter request</param>
        /// <returns>Paginated car responses</returns>
        Task<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>> GetCarsAsync(CarFilterRequest request);

        /// <summary>
        /// Gets cars for a specific vendor
        /// </summary>
        /// <param name="vendorId">The vendor ID</param>
        /// <param name="request">The filter request</param>
        /// <returns>Paginated car responses</returns>
        Task<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>> GetVendorCarsAsync(Guid vendorId, CarFilterRequest request);

        /// <summary>
        /// Gets cars for a specific user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="request">The filter request</param>
        /// <returns>Paginated car responses</returns>
        Task<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>> GetUserCarsAsync(Guid userId, CarFilterRequest request);

        /// <summary>
        /// Deletes a car listing (soft delete)
        /// </summary>
        /// <param name="carId">The ID of the car to delete</param>
        /// <param name="userId">The ID of the user deleting the car</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> DeleteCarAsync(Guid carId, Guid userId);

        /// <summary>
        /// Marks a car as sold
        /// </summary>
        /// <param name="carId">The ID of the car</param>
        /// <param name="userId">The ID of the user marking as sold</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> MarkCarAsSoldAsync(Guid carId, Guid userId);

        /// <summary>
        /// Uploads car images to AWS S3 and updates car status
        /// </summary>
        /// <param name="carId">The ID of the car</param>
        /// <param name="images">The image files to upload</param>
        /// <param name="primaryImageIndex">Index of the primary image (0-based)</param>
        /// <param name="userId">The ID of the user uploading images</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> UploadCarImagesAsync(Guid carId, List<IFormFile> images, int? primaryImageIndex, Guid userId);

        /// <summary>
        /// Updates car images
        /// </summary>
        /// <param name="carId">The ID of the car</param>
        /// <param name="images">The new image files to upload</param>
        /// <param name="primaryImageIndex">Index of the primary image (0-based)</param>
        /// <param name="userId">The ID of the user updating images</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> UpdateCarImagesAsync(Guid carId, List<IFormFile> images, int? primaryImageIndex, Guid userId);

        /// <summary>
        /// Increments the view count for a car
        /// </summary>
        /// <param name="carId">The ID of the car</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> IncrementViewCountAsync(Guid carId);

        /// <summary>
        /// Increments the inquiry count for a car
        /// </summary>
        /// <param name="carId">The ID of the car</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> IncrementInquiryCountAsync(Guid carId);

        /// <summary>
        /// Gets featured cars
        /// </summary>
        /// <param name="count">Number of featured cars to return</param>
        /// <returns>Featured car responses</returns>
        Task<ApiResponse<List<CarSummaryResponseDto>>> GetFeaturedCarsAsync(int count = 10);

        /// <summary>
        /// Gets recently added cars
        /// </summary>
        /// <param name="count">Number of recent cars to return</param>
        /// <returns>Recent car responses</returns>
        Task<ApiResponse<List<CarSummaryResponseDto>>> GetRecentCarsAsync(int count = 10);

        /// <summary>
        /// Gets car statistics for dashboard
        /// </summary>
        /// <param name="userId">The user ID (optional, for user-specific stats)</param>
        /// <param name="vendorId">The vendor ID (optional, for vendor-specific stats)</param>
        /// <returns>Car statistics</returns>
        Task<ApiResponse<CarStatisticsResponseDto>> GetCarStatisticsAsync(Guid? userId = null, Guid? vendorId = null);

        /// <summary>
        /// Approves a car listing (Admin only)
        /// </summary>
        /// <param name="carId">The ID of the car to approve</param>
        /// <param name="request">Approval request details</param>
        /// <param name="adminUserId">The ID of the admin user performing the approval</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> ApproveCarAsync(Guid carId, ApproveCarRequest request, Guid adminUserId);

        /// <summary>
        /// Rejects a car listing (Admin only)
        /// </summary>
        /// <param name="carId">The ID of the car to reject</param>
        /// <param name="request">Rejection request details</param>
        /// <param name="adminUserId">The ID of the admin user performing the rejection</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> RejectCarAsync(Guid carId, RejectCarRequest request, Guid adminUserId);
    }

    /// <summary>
    /// Response DTO for car statistics
    /// </summary>
    public class CarStatisticsResponseDto
    {
        public int TotalCars { get; set; }
        public int ActiveCars { get; set; }
        public int SoldCars { get; set; }
        public int DraftCars { get; set; }
        public int FeaturedCars { get; set; }
        public decimal AveragePrice { get; set; }
        public int TotalViews { get; set; }
        public int TotalInquiries { get; set; }
        public Dictionary<string, int> CarsByBrand { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> CarsByCondition { get; set; } = new Dictionary<string, int>();
        public Dictionary<int, int> CarsByYear { get; set; } = new Dictionary<int, int>();
    }
}
