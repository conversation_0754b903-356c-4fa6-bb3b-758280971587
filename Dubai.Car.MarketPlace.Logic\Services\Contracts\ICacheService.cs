using System.Threading.Tasks;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts;

/// <summary>
/// Interface for caching service operations
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Get data from cache
    /// </summary>
    /// <typeparam name="T">Type of data to retrieve</typeparam>
    /// <param name="key">Cache key</param>
    Task<T?> GetAsync<T>(string key) where T : class;

    /// <summary>
    /// Set data in cache
    /// </summary>
    /// <typeparam name="T">Type of data to cache</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="value">Data to cache</param>
    /// <param name="expirationMinutes">Cache expiration in minutes</param>
    Task SetAsync<T>(string key, T value, int expirationMinutes = 30) where T : class;

    /// <summary>
    /// Remove data from cache
    /// </summary>
    /// <param name="key">Cache key</param>
    Task RemoveAsync(string key);

    /// <summary>
    /// Check if key exists in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    Task<bool> ExistsAsync(string key);
} 