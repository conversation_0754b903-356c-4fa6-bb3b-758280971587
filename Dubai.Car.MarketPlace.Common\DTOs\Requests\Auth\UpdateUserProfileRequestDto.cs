﻿using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth
{
    /// <summary>
    /// DTO for updating user profile information
    /// </summary>
    public class UpdateUserProfileRequestDto
    {
        /// <summary>
        /// User's first name
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
        private string _firstName { get; set; } = default!;
        public string FirstName
        {
            get => _firstName;
            set => _firstName = char.ToUpper(value[0]) + value.Substring(1).Trim();
        }
        
        /// <summary>
        /// User's last name
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
        private string _lastName { get; set; } = default!;
        public string LastName
        {
            get => _lastName;
            set => _lastName = char.ToUpper(value[0]) + value.Substring(1).Trim();
        }
        
        /// <summary>
        /// User's gender
        /// </summary>
        [StringLength(20, ErrorMessage = "Gender cannot exceed 20 characters")]
        public string Gender { get; set; } = string.Empty;
        
        /// <summary>
        /// User's designation or job title
        /// </summary>
        [StringLength(100, ErrorMessage = "Designation cannot exceed 100 characters")]
        public string? Designation { get; set; }
        
        /// <summary>
        /// User's phone number
        /// </summary>
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        [Phone(ErrorMessage = "Invalid phone number format")]
        public string? PhoneNumber { get; set; }
        
        /// <summary>
        /// Base64 encoded profile picture
        /// </summary>
        public string? ProfilePictureBase64 { get; set; }
        
        /// <summary>
        /// Filename for the profile picture
        /// </summary>
        public string? ImageFileName { get; set; }
    }
}
