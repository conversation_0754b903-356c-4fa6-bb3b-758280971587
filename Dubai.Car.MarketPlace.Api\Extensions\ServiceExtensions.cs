﻿using AspNetCoreRateLimit;
using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Dubai.Car.MarketPlace.Logic.Services.Impl;
using StackExchange.Redis;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Service extention class
    /// </summary>
    public static class ServiceExtensions
    {        /// <summary>
             /// AddDIServices method. Used to add services to the DI container.
             /// </summary>
             /// <param name="services">Service collection to add services to</param>
             /// <param name="config">Application configuration</param>
             /// <param name="conn">Connection string</param>
             /// <remarks>
             /// The following configurations are added:
             /// 1. Swagger
             /// 2. WatchDog
             /// 3. CorsPolicy
             /// 4. JwtAuthentication
             /// </remarks>
        public static void AddDIServices(this IServiceCollection services, IConfiguration config, string conn)
        {
            services.AddWatchDogConfig(conn);
            services.AddSwaggerConfig();
            services.AddCorsPolicy();
            services.AddJwtAuthentication(config);

            // Configure IOptions like JwtSettings
            services.Configure<JwtSettings>(config.GetSection("JwtCredentials"));

            // Add auto mapper
            services.AddAutoMapper(typeof(Dubai.Car.MarketPlace.Logic.Helper.AutoMapper));

            // Add AspNetCoreRateLimit services
            services.AddMemoryCache();
            services.Configure<IpRateLimitOptions>(config.GetSection("IpRateLimiting"));
            services.Configure<IpRateLimitPolicies>(config.GetSection("IpRateLimitPolicies"));

            // Infrastructure services
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IJwtService, JwtService>();
            services.AddScoped<IAwsService, AwsService>();
            services.AddScoped<IUaePassService, UaePassService>();

            // Configure application settings from configuration
            services.Configure<EmailSettings>(config.GetSection("EmailSettings"));
            services.Configure<AppSettings>(config.GetSection("AppSettings"));
            services.Configure<JwtSettings>(config.GetSection("JwtCredentials"));
            services.Configure<AwsSettings>(config.GetSection("AwsSettings"));
            services.Configure<UaePassSettings>(config.GetSection("UaePassSettings"));

            services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
            services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
            services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();

            // Configure HttpClient for UAE PASS
            services.AddHttpClient("UaePass", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "Dubai-Car-MarketPlace-Api/1.0");
            });

            // Business logic services
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IVendorService, VendorService>();
            services.AddScoped<IAdminService, AdminService>();

            // Car management services
            services.AddScoped<ICarService, CarService>();
            services.AddScoped<ICarLookupService, CarLookupService>();
            services.AddScoped<ICarSpecificationService, CarSpecificationService>();
        }
    }
}
