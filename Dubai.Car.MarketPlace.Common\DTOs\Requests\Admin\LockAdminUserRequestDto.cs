using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin
{
    /// <summary>
    /// Request DTO for locking or unlocking an admin user
    /// </summary>
    public class LockAdminUserRequestDto
    {
        /// <summary>
        /// Whether to lock the admin user
        /// </summary>
        public bool IsLocked { get; set; }

        /// <summary>
        /// Reason for locking/unlocking the admin user
        /// </summary>
        [StringLength(500)]
        public string? Reason { get; set; }
    }
}
