using System.ComponentModel.DataAnnotations;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a car specification template that can be used for car listings
    /// </summary>
    public class CarSpecification : BaseEntity
    {
        /// <summary>
        /// The name of the specification (e.g., "Engine Power", "Fuel Consumption")
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = default!;

        /// <summary>
        /// The category of the specification for grouping
        /// </summary>
        [Required]
        public SpecificationCategory Category { get; set; }

        /// <summary>
        /// The data type of the specification value
        /// </summary>
        [Required]
        public SpecificationDataType DataType { get; set; }

        /// <summary>
        /// The unit of measurement (e.g., "HP", "L/100km", "mm")
        /// </summary>
        [MaxLength(20)]
        public string? Unit { get; set; }

        /// <summary>
        /// The description of the specification
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Whether this specification is required for car listings
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// Whether this specification is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting specifications in UI
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Predefined options for dropdown/select specifications (JSON array)
        /// </summary>
        public string? PredefinedOptions { get; set; }

        /// <summary>
        /// Minimum value for numeric specifications
        /// </summary>
        public decimal? MinValue { get; set; }

        /// <summary>
        /// Maximum value for numeric specifications
        /// </summary>
        public decimal? MaxValue { get; set; }

        /// <summary>
        /// Regular expression pattern for text validation
        /// </summary>
        [MaxLength(200)]
        public string? ValidationPattern { get; set; }

        /// <summary>
        /// Help text to display to users
        /// </summary>
        [MaxLength(500)]
        public string? HelpText { get; set; }

        // Navigation properties
        /// <summary>
        /// Car specification values associated with this specification
        /// </summary>
        public virtual ICollection<CarSpecificationValue> CarSpecificationValues { get; set; } = new List<CarSpecificationValue>();
    }
}
