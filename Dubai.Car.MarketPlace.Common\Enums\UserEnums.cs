﻿namespace Dubai.Car.MarketPlace.Common.Enums
{
    public class UserEnums
    {
        /// <summary>
        /// Represents the status of a user in the system
        /// </summary>
        public enum UserStatus
        {
            /// <summary>
            /// User is active and can use all features
            /// </summary>
            Active = 1,

            /// <summary>
            /// User is inactive and cannot use any features
            /// </summary>
            Inactive = 2,

            /// <summary>
            /// User is suspended due to violations
            /// </summary>
            Suspended = 3,

            /// <summary>
            /// User is banned permanently
            /// </summary>
            Banned = 4,

            /// <summary>
            /// User's email is pending verification
            /// </summary>
            PendingVerification = 5
        }
    }
}
