﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\Dubai.Car.MarketPlace.Common\Dubai.Car.MarketPlace.Common.csproj" />
		<ProjectReference Include="..\Dubai.Car.MarketPlace.Data\Dubai.Car.MarketPlace.Data.csproj" />
		<PackageReference Include="AWSSDK.S3" Version="4.0.2" />
		<PackageReference Include="RazorEngineCore" Version="2024.4.1" />
		<PackageReference Include="RestSharp" Version="112.1.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.7.27" />
		<PackageReference Include="WatchDog.NET" Version="1.4.11" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.5" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
	</ItemGroup>

</Project>
