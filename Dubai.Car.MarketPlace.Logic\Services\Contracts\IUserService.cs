﻿using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using System.Threading.Tasks;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Interface for user-related operations
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// Get user profile by ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User profile information</returns>
        Task<ApiResponse<UserProfileResponseDto>> GetUserProfileAsync(long userId);
        
        /// <summary>
        /// Update user profile
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="model">Updated profile information</param>
        /// <returns>Updated user profile</returns>
        Task<ApiResponse<UserProfileResponseDto>> UpdateUserProfileAsync(long userId, UpdateUserProfileRequestDto model);

        /// <summary>
        /// Get all users with pagination
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated list of users</returns>
        Task<ApiResponse<PaginatedResponseDto<UserProfileResponseDto>>> GetAllUsersAsync(int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// Get current user's permissions
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User permissions information</returns>
        Task<ApiResponse<UserPermissionsResponseDto>> GetUserPermissionsAsync(long userId);
    }
}
