using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a fuel type in the system
    /// </summary>
    public class FuelType : BaseEntity
    {
        /// <summary>
        /// The name of the fuel type (e.g., Petrol, Diesel, Electric, Hybrid)
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = default!;

        /// <summary>
        /// The description of the fuel type
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// The icon image key for storage (S3, etc.)
        /// </summary>
        public string? IconImageKey { get; set; }

        /// <summary>
        /// Whether this fuel type is currently active for new car listings
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting fuel types in UI
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Environmental impact rating (1-10, 10 being most eco-friendly)
        /// </summary>
        public int? EnvironmentalRating { get; set; }

        // Navigation properties
        /// <summary>
        /// Cars associated with this fuel type
        /// </summary>
        public virtual ICollection<Car> Cars { get; set; } = new List<Car>();
    }
}
