using Dubai.Car.MarketPlace.Api.Extensions;
using Dubai.Car.MarketPlace.Data.Database;
using Microsoft.EntityFrameworkCore;
using System.Configuration;
using AspNetCoreRateLimit;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

// Add services to the container.

builder.Services.AddControllers();
builder.Services.AddRouting(options =>
{
    options.LowercaseUrls = true;
});

// Add rate limiting services
builder.Services.AddRateLimitServices(configuration);

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

// Get the environment
var connectionString = "";
var env = builder.Environment;
if (env.IsDevelopment())
    connectionString = builder.Configuration.GetConnectionString("LocalConnectionString");
else
    connectionString = builder.Configuration.GetConnectionString("ConnectionString");
if (string.IsNullOrEmpty(connectionString))
    throw new Exception("Connection string is not set in the configuration.");

builder.Services.AddDbContext<CarContext>(options => options.UseNpgsql(connectionString));

// Add Identity services
builder.Services.AddIdentityServiceConfig();

builder.Services.AddDIServices(configuration, connectionString);

// Add health checks with UI
builder.Services.AddHealthCheckServices(configuration, connectionString);

// Add Hangfire services
builder.Services.AddHangfireServices(configuration, connectionString);

using var scope = builder.Services.BuildServiceProvider().CreateScope();
var dbContext = scope.ServiceProvider.GetRequiredService<CarContext>();

// Check for pending migrations before applying them
var pendingMigrations = dbContext.Database.GetPendingMigrations();
if (pendingMigrations.Any())
{
    dbContext.Database.Migrate();
}

builder.Services.AddSwaggerGen();

// Add database seeding service
builder.Services.AddDatabaseSeeding();

var app = builder.Build();

// Seed the database with roles and permissions
using (var seedScope = app.Services.CreateScope())
{
    try
    {
        await seedScope.ServiceProvider.SeedDatabaseAsync();
    }
    catch (Exception ex)
    {
        var logger = seedScope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database.");
    }
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add CORS Policy
app.UseCors("CorsPolicy");

app.UseSwagger();
app.UseSwaggerUI();

app.UseHttpsRedirection();

// Add rate limiting middleware
app.UseIpRateLimiting();

app.UseAuthorization();

app.MapControllers();

app.Run();
