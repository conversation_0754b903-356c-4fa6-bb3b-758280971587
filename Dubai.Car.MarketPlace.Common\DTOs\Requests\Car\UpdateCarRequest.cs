using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Car
{
    /// <summary>
    /// Request DTO for updating an existing car listing
    /// </summary>
    public class UpdateCarRequest
    {
        /// <summary>
        /// The model name of the car
        /// </summary>
        [StringLength(100, ErrorMessage = "Model cannot exceed 100 characters")]
        public string? Model { get; set; }

        /// <summary>
        /// The manufacturing year of the car
        /// </summary>
        [Range(1900, 2030, ErrorMessage = "Year must be between 1900 and 2030")]
        public int? Year { get; set; }

        /// <summary>
        /// The price of the car in AED
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal? Price { get; set; }

        /// <summary>
        /// The mileage/odometer reading in kilometers
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Mileage cannot be negative")]
        public int? Mileage { get; set; }

        /// <summary>
        /// The color of the car
        /// </summary>
        [StringLength(50, ErrorMessage = "Color cannot exceed 50 characters")]
        public string? Color { get; set; }

        /// <summary>
        /// The VIN (Vehicle Identification Number)
        /// </summary>
        [StringLength(17, MinimumLength = 17, ErrorMessage = "VIN must be exactly 17 characters")]
        public string? VIN { get; set; }

        /// <summary>
        /// The license plate number
        /// </summary>
        [StringLength(20, ErrorMessage = "License plate cannot exceed 20 characters")]
        public string? LicensePlate { get; set; }

        /// <summary>
        /// The engine size in liters
        /// </summary>
        [Range(0.1, 20.0, ErrorMessage = "Engine size must be between 0.1 and 20.0 liters")]
        public decimal? EngineSize { get; set; }

        /// <summary>
        /// The transmission type
        /// </summary>
        public TransmissionType? Transmission { get; set; }

        /// <summary>
        /// The number of doors
        /// </summary>
        [Range(2, 6, ErrorMessage = "Number of doors must be between 2 and 6")]
        public int? NumberOfDoors { get; set; }

        /// <summary>
        /// The number of seats
        /// </summary>
        [Range(1, 12, ErrorMessage = "Number of seats must be between 1 and 12")]
        public int? NumberOfSeats { get; set; }

        /// <summary>
        /// The condition of the car
        /// </summary>
        public CarCondition? Condition { get; set; }

        /// <summary>
        /// The current status of the car listing
        /// </summary>
        public CarStatus? Status { get; set; }

        /// <summary>
        /// Description of the car
        /// </summary>
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Features of the car
        /// </summary>
        public List<string>? Features { get; set; }

        /// <summary>
        /// Whether the car is featured
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Whether the car is sold
        /// </summary>
        public bool? IsSold { get; set; }

        /// <summary>
        /// The date when the listing expires
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        // Foreign Keys
        /// <summary>
        /// The ID of the brand
        /// </summary>
        public Guid? BrandId { get; set; }

        /// <summary>
        /// The ID of the body type
        /// </summary>
        public Guid? BodyTypeId { get; set; }

        /// <summary>
        /// The ID of the fuel type
        /// </summary>
        public Guid? FuelTypeId { get; set; }

        /// <summary>
        /// The ID of the vendor (optional for individual sellers)
        /// </summary>
        public Guid? VendorId { get; set; }

        /// <summary>
        /// Specification values for this car
        /// </summary>
        public List<UpdateCarSpecificationValueRequest>? SpecificationValues { get; set; }

        /// <summary>
        /// Optional car images to upload (will replace existing images if provided)
        /// </summary>
        public List<IFormFile>? Images { get; set; }

        /// <summary>
        /// Index of the primary image in the Images list (0-based)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Primary image index must be 0 or greater")]
        public int? PrimaryImageIndex { get; set; }
    }

    /// <summary>
    /// Request DTO for updating a car specification value
    /// </summary>
    public class UpdateCarSpecificationValueRequest
    {
        /// <summary>
        /// The ID of the specification value (if updating existing)
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// The ID of the specification template
        /// </summary>
        [Required(ErrorMessage = "Specification ID is required")]
        public Guid CarSpecificationId { get; set; }

        /// <summary>
        /// The value of the specification
        /// </summary>
        [Required(ErrorMessage = "Value is required")]
        [StringLength(500, ErrorMessage = "Value cannot exceed 500 characters")]
        public string Value { get; set; } = default!;

        /// <summary>
        /// Additional notes or comments about this specification value
        /// </summary>
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether to delete this specification value
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }

    /// <summary>
    /// Request DTO for uploading car images
    /// </summary>
    public class UploadCarImagesRequest
    {
        /// <summary>
        /// List of image files to upload
        /// </summary>
        [Required(ErrorMessage = "At least one image is required")]
        [MinLength(1, ErrorMessage = "At least one image is required")]
        public List<IFormFile> Images { get; set; } = new List<IFormFile>();

        /// <summary>
        /// Index of the primary image in the Images list (0-based)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Primary image index must be 0 or greater")]
        public int? PrimaryImageIndex { get; set; }
    }

    /// <summary>
    /// Request DTO for approving a car listing
    /// </summary>
    public class ApproveCarRequest
    {
        /// <summary>
        /// Optional approval notes/comments
        /// </summary>
        [StringLength(1000, ErrorMessage = "Approval notes cannot exceed 1000 characters")]
        public string? ApprovalNotes { get; set; }

        /// <summary>
        /// Whether to mark the car as featured upon approval
        /// </summary>
        public bool MarkAsFeatured { get; set; } = false;
    }

    /// <summary>
    /// Request DTO for rejecting a car listing
    /// </summary>
    public class RejectCarRequest
    {
        /// <summary>
        /// Reason for rejection (required)
        /// </summary>
        [Required(ErrorMessage = "Rejection reason is required")]
        [StringLength(1000, MinimumLength = 10, ErrorMessage = "Rejection reason must be between 10 and 1000 characters")]
        public string RejectionReason { get; set; } = default!;

        /// <summary>
        /// Additional notes for the rejection
        /// </summary>
        [StringLength(1000, ErrorMessage = "Additional notes cannot exceed 1000 characters")]
        public string? AdditionalNotes { get; set; }
    }
}
