using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Security.Claims;

namespace Dubai.Car.MarketPlace.Api.Attributes
{
    /// <summary>
    /// Custom authorization attribute for Super Admin access
    /// </summary>
    public class SuperAdminAuthorizationAttribute : AuthorizeAttribute, IAuthorizationFilter
    {
        /// <summary>
        /// Initializes a new instance of the SuperAdminAuthorizationAttribute class
        /// </summary>
        public SuperAdminAuthorizationAttribute()
        {
            Roles = "Super Admin";
        }

        /// <summary>
        /// Executes the authorization filter
        /// </summary>
        /// <param name="context">Authorization filter context</param>
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var user = context.HttpContext.User;

            // Check if user is authenticated
            if (!user.Identity!.IsAuthenticated)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Check if user has the Super Admin role
            if (!user.IsInRole("Super Admin"))
            {
                context.Result = new ForbidResult();
                return;
            }

            // Additional checks can be added here if needed
            // For example, checking if the user's account is active, not locked, etc.
        }
    }
}
