namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Complete dashboard data response DTO
    /// </summary>
    public class DashboardResponseDto
    {
        /// <summary>
        /// Dashboard statistics
        /// </summary>
        public DashboardStatsResponseDto Stats { get; set; } = new();

        /// <summary>
        /// Pending vendor applications (top 5)
        /// </summary>
        public List<DashboardVendorApplicationResponseDto> PendingVendorApplications { get; set; } = new();

        /// <summary>
        /// Recent support tickets
        /// </summary>
        public DashboardSupportTicketsResponseDto SupportTickets { get; set; } = new();

        /// <summary>
        /// Platform revenue data
        /// </summary>
        public DashboardRevenueResponseDto Revenue { get; set; } = new();
    }

    /// <summary>
    /// Support tickets summary for dashboard
    /// </summary>
    public class DashboardSupportTicketsResponseDto
    {
        /// <summary>
        /// Number of open tickets
        /// </summary>
        public int OpenTickets { get; set; }

        /// <summary>
        /// Number of tickets in progress
        /// </summary>
        public int InProgressTickets { get; set; }

        /// <summary>
        /// Number of tickets resolved today
        /// </summary>
        public int ResolvedToday { get; set; }

        /// <summary>
        /// Recent tickets (last 10)
        /// </summary>
        public List<DashboardSupportTicketResponseDto> RecentTickets { get; set; } = new();
    }
}
