using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a vendor in the system (renamed from VendorApplication table).
    /// </summary>
    public class Vendor
    {
        /// <summary>
        /// The unique ID of the vendor
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// The business name of the vendor
        /// </summary>
        public string BusinessName { get; set; } = default!;

        /// <summary>
        /// The contact person's name
        /// </summary>
        public string ContactPersonName { get; set; } = default!;

        /// <summary>
        /// The position of the contact person
        /// </summary>
        public string Position { get; set; } = default!;

        /// <summary>
        /// The business email
        /// </summary>
        public string BusinessEmail { get; set; } = default!;

        /// <summary>
        /// The phone number
        /// </summary>
        public string PhoneNumber { get; set; } = default!;

        /// <summary>
        /// The trade license number
        /// </summary>
        public string TradeLicenseNumber { get; set; } = default!;

        /// <summary>
        /// The business address
        /// </summary>
        public string BusinessAddress { get; set; } = default!;

        /// <summary>
        /// The city where the business is located
        /// </summary>
        public string City { get; set; } = default!;

        /// <summary>
        /// The business specialization (car brands)
        /// </summary>
        public string? Specialization { get; set; }

        /// <summary>
        /// The business description
        /// </summary>
        public string? BusinessDescription { get; set; }

        /// <summary>
        /// The status of the vendor application
        /// </summary>
        public VendorApplicationStatus Status { get; set; } = VendorApplicationStatus.Pending;

        /// <summary>
        /// The date when the application was submitted
        /// </summary>
        public DateTime SubmissionDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The date when the application was reviewed
        /// </summary>
        public DateTime? ReviewDate { get; set; }

        /// <summary>
        /// The ID of the admin who reviewed the application
        /// </summary>
        public Guid? ReviewedByUserId { get; set; }

        /// <summary>
        /// The admin who reviewed the application
        /// </summary>
        public virtual User? ReviewedByUser { get; set; }

        /// <summary>
        /// Comments or notes about the application review
        /// </summary>
        public string? ReviewComments { get; set; }

        /// <summary>
        /// The ID of the user associated with this vendor application
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// The user associated with this vendor application
        /// </summary>
        public virtual User? User { get; set; }

        /// <summary>
        /// Trade license document key for storage (S3, etc.)
        /// </summary>
        public string? TradeLicenseDocumentKey { get; set; }

        /// <summary>
        /// Additional document keys for storage (S3, etc.) - JSON array
        /// </summary>
        public string? AdditionalDocumentKeys { get; set; }

        /// <summary>
        /// The subscription plan selected after approval
        /// </summary>
        public string? SelectedSubscriptionPlan { get; set; }

        // Additional vendor properties for when approved
        /// <summary>
        /// The vendor's rating (1-5 stars)
        /// </summary>
        public decimal? Rating { get; set; }

        /// <summary>
        /// Total number of ratings received
        /// </summary>
        public int TotalRatings { get; set; } = 0;

        /// <summary>
        /// Whether the vendor is verified
        /// </summary>
        public bool IsVerified { get; set; } = false;

        /// <summary>
        /// Whether the vendor is featured
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// The vendor's logo image key for storage (S3, etc.)
        /// </summary>
        public string? LogoImageKey { get; set; }

        /// <summary>
        /// The subscription plan of the vendor
        /// </summary>
        public string? SubscriptionPlan { get; set; }

        /// <summary>
        /// The subscription expiry date
        /// </summary>
        public DateTime? SubscriptionExpiryDate { get; set; }

        // BaseEntity properties
        /// <summary>
        /// Flag indicating if this record has been soft deleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// The date when the record was created
        /// </summary>
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The date when the record was last modified
        /// </summary>
        public DateTime? ModifiedOn { get; set; }

        /// <summary>
        /// The date when the record was deleted (if applicable)
        /// </summary>
        public DateTime? DeletedOn { get; set; }

        // Navigation properties
        /// <summary>
        /// Cars listed by this vendor
        /// </summary>
        public virtual ICollection<Car> Cars { get; set; } = new List<Car>();

        public Vendor()
        {
            Id = Guid.NewGuid();
        }
    }
}
