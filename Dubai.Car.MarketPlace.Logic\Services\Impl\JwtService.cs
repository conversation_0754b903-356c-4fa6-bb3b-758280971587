﻿using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using WatchDog;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    public class JwtService : IJwtService
    {
        private readonly JwtSettings _jwtSettings;
        private readonly CarContext _dbContext;
        private readonly UserManager<User> _userManager;

        public JwtService(
            IOptions<JwtSettings> jwtSettings,
            CarContext dbContext,
            UserManager<User> userManager)
        {
            _jwtSettings = jwtSettings.Value;
            _dbContext = dbContext;
            _userManager = userManager;
        }

        #region Generate JWT Token
        public async Task<string> GenerateJwtToken(User user)
        {
            // Create basic claims
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
                new Claim(ClaimTypes.Name, $"{user.FirstName} {user.LastName}"),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            // Get user roles and add them to claims
            var userRoles = await _userManager.GetRolesAsync(user);
            foreach (var role in userRoles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Get user permissions from roles and add them to claims
            var permissions = await GetUserPermissionsAsync(user);
            foreach (var permission in permissions)
            {
                claims.Add(new Claim("permission", permission));
            }

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.UtcNow.AddMinutes(_jwtSettings.Lifetime);

            var token = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: claims,
                expires: expires,
                signingCredentials: creds
            );

            WatchLogger.Log($"JWT token generated for user: {user.Email} with roles: {string.Join(", ", userRoles)} and permissions: {string.Join(", ", permissions)}", "GenerateJwtToken");
            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        #endregion

        #region Generate Refresh Token
        public async Task<string> GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }
        #endregion

        #region Validate Token
        public async Task<ClaimsPrincipal> GetPrincipalFromExpiredToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = false, // We don't care about the token's expiration date
                ValidateIssuerSigningKey = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidAudience = _jwtSettings.Audience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey))
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken securityToken;
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out securityToken);
            var jwtSecurityToken = securityToken as JwtSecurityToken;

            if (jwtSecurityToken == null || !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                throw new SecurityTokenException("Invalid token");

            return principal;
        }
        #endregion

        #region Validate Refresh Token
        public async Task<bool> BlacklistToken(string token)
        {
            var blacklistedToken = new BlackListedToken
            {
                Token = token,
                BlackListedOn = DateTime.UtcNow
            };

            await _dbContext.BlackListedTokens.AddAsync(blacklistedToken);
            await _dbContext.SaveChangesAsync();

            WatchLogger.Log($"Token blacklisted successfully", "BlacklistToken");
            return true;
        }
        #endregion

        #region Check if Token is Blacklisted
        public async Task<bool> IsTokenBlacklisted(string token)
        {
            return await _dbContext.BlackListedTokens.AnyAsync(t => t.Token == token);
        }
        #endregion

        #region Get User Permissions
        /// <summary>
        /// Gets all permissions for a user based on their roles
        /// </summary>
        /// <param name="user">The user to get permissions for</param>
        /// <returns>List of permission names</returns>
        private async Task<List<string>> GetUserPermissionsAsync(User user)
        {
            var permissions = new List<string>();
            
            // Get user roles
            var userRoles = await _userManager.GetRolesAsync(user);
            
            if (!userRoles.Any())
                return permissions;

            // Get role IDs from role names
            var roleIds = new List<Guid>();
            foreach (var roleName in userRoles)
            {
                var role = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Name == roleName);
                if (role != null)
                {
                    roleIds.Add(role.Id);
                }
            }

            if (!roleIds.Any())
                return permissions;

            // Get permissions for all user roles
            var rolePermissions = await _dbContext.RolePermissions
                .Include(rp => rp.Permission)
                .Where(rp => roleIds.Contains(rp.RoleId))
                .Select(rp => rp.Permission.Name)
                .Distinct()
                .ToListAsync();

            return rolePermissions;
        }

        /// <summary>
        /// Gets all permissions for a user as SystemPermissions enum
        /// </summary>
        /// <param name="user">The user to get permissions for</param>
        /// <returns>List of SystemPermissions</returns>
        public async Task<List<RoleEnums.SystemPermissions>> GetUserPermissionsEnumAsync(User user)
        {
            var permissionNames = await GetUserPermissionsAsync(user);
            var systemPermissions = new List<RoleEnums.SystemPermissions>();

            foreach (var permissionName in permissionNames)
            {
                if (Enum.TryParse<RoleEnums.SystemPermissions>(permissionName, out var permission))
                {
                    systemPermissions.Add(permission);
                }
            }

            return systemPermissions;
        }
        #endregion
    }
}
