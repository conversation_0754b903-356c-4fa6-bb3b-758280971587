namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for platform revenue data in dashboard
    /// </summary>
    public class DashboardRevenueResponseDto
    {
        /// <summary>
        /// Current month revenue
        /// </summary>
        public decimal CurrentMonthRevenue { get; set; }

        /// <summary>
        /// Last month revenue
        /// </summary>
        public decimal LastMonthRevenue { get; set; }

        /// <summary>
        /// Growth percentage compared to last month
        /// </summary>
        public decimal GrowthPercentage { get; set; }

        /// <summary>
        /// Monthly revenue data for the chart (last 12 months)
        /// </summary>
        public List<MonthlyRevenueDto> MonthlyRevenue { get; set; } = new();

        /// <summary>
        /// Revenue breakdown by source
        /// </summary>
        public List<RevenueSourceDto> RevenueBySource { get; set; } = new();
    }

    /// <summary>
    /// Monthly revenue data point
    /// </summary>
    public class MonthlyRevenueDto
    {
        /// <summary>
        /// Month name (e.g., "<PERSON>", "Feb")
        /// </summary>
        public string Month { get; set; } = default!;

        /// <summary>
        /// Revenue amount for the month
        /// </summary>
        public decimal Revenue { get; set; }

        /// <summary>
        /// Year
        /// </summary>
        public int Year { get; set; }
    }

    /// <summary>
    /// Revenue source breakdown
    /// </summary>
    public class RevenueSourceDto
    {
        /// <summary>
        /// Source name (e.g., "Subscription Fees", "Commission", "Advertising")
        /// </summary>
        public string Source { get; set; } = default!;

        /// <summary>
        /// Revenue amount from this source
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Percentage of total revenue
        /// </summary>
        public decimal Percentage { get; set; }
    }
}
