﻿using Dubai.Car.MarketPlace.Data.Database;
using Microsoft.EntityFrameworkCore;
using System.Net;

namespace Dubai.Car.MarketPlace.Api.Middlewares
{
    /// <summary>
    /// TokenValidationMiddleware class
    /// </summary>
    /// <param name="next"></param>
    /// <param name="factory"></param>
    public class TokenValidationMiddleware(
        IServiceScopeFactory factory,
        RequestDelegate next)
    {
        /// <summary>
        /// InvokeAsync method
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task InvokeAsync(HttpContext context)
        {
            // Get the db context
            using var scope = factory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<CarContext>();

            var token = context.Request.Headers["Authorization"].ToString().Split(" ").Last();
            var blackListedToken = await dbContext.BlackListedTokens.FirstOrDefaultAsync(x => x.Token == token);
            if (blackListedToken != null)
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                await context.Response.WriteAsync("You logged out, please login.");
                return;
            }

            await next(context);
        }
    }
}
