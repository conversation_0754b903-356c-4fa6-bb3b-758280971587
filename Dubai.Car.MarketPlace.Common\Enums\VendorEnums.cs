namespace Dubai.Car.MarketPlace.Common.Enums
{
    public class VendorEnums
    {
        /// <summary>
        /// Represents the status of a vendor application in the system
        /// </summary>
        public enum VendorApplicationStatus
        {
            /// <summary>
            /// Application is pending review
            /// </summary>
            Pending = 1,

            /// <summary>
            /// Application is under review
            /// </summary>
            UnderReview = 2,

            /// <summary>
            /// Application has been approved
            /// </summary>
            Approved = 3,

            /// <summary>
            /// Application has been rejected
            /// </summary>
            Rejected = 4,

            /// <summary>
            /// Additional information is required for the application
            /// </summary>
            AdditionalInfoRequired = 5
        }

        /// <summary>
        /// Represents the user types in the system
        /// </summary>
        public enum UserType
        {
            /// <summary>
            /// Admin user (Super Admin, Content Admin, Support Admin, Financial Admin)
            /// </summary>
            Admin = 1,

            /// <summary>
            /// Vendor user
            /// </summary>
            Vendor = 2,

            /// <summary>
            /// Regular customer
            /// </summary>
            Customer = 3,

            /// <summary>
            /// Private seller
            /// </summary>
            PrivateSeller = 4
        }

        /// <summary>
        /// Represents user roles in the system
        /// </summary>
        public enum UserRole
        {
            /// <summary>
            /// Super Admin role with full system access
            /// </summary>
            SuperAdmin = 1,

            /// <summary>
            /// Regular customer role
            /// </summary>
            Customer = 2,

            /// <summary>
            /// Content admin role
            /// </summary>
            ContentAdmin = 3,

            /// <summary>
            /// Vendor role
            /// </summary>
            Vendor = 4,

            /// <summary>
            /// Private seller role
            /// </summary>
            PrivateSeller = 5,

            /// <summary>
            /// Support admin role
            /// </summary>
            SupportAdmin = 6,

            /// <summary>
            /// Financial admin role
            /// </summary>
            FinancialAdmin = 7
        }
    }
}
