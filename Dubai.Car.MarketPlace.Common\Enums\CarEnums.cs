namespace Dubai.Car.MarketPlace.Common.Enums
{
    public class CarEnums
    {
        /// <summary>
        /// Represents the status of a car listing
        /// </summary>
        public enum CarStatus
        {
            /// <summary>
            /// Car listing is in draft mode and not published
            /// </summary>
            Draft = 1,

            /// <summary>
            /// Car listing is active and visible to buyers
            /// </summary>
            Active = 2,

            /// <summary>
            /// Car listing is temporarily inactive
            /// </summary>
            Inactive = 3,

            /// <summary>
            /// Car listing is pending admin approval
            /// </summary>
            PendingApproval = 4,

            /// <summary>
            /// Car listing has been rejected by admin
            /// </summary>
            Rejected = 5,

            /// <summary>
            /// Car has been sold
            /// </summary>
            Sold = 6,

            /// <summary>
            /// Car listing has expired
            /// </summary>
            Expired = 7,

            /// <summary>
            /// Car listing has been suspended due to violations
            /// </summary>
            Suspended = 8
        }

        /// <summary>
        /// Represents the condition of a car
        /// </summary>
        public enum CarCondition
        {
            /// <summary>
            /// Brand new car, never used
            /// </summary>
            New = 1,

            /// <summary>
            /// Used car in excellent condition
            /// </summary>
            Excellent = 2,

            /// <summary>
            /// Used car in very good condition
            /// </summary>
            VeryGood = 3,

            /// <summary>
            /// Used car in good condition
            /// </summary>
            Good = 4,

            /// <summary>
            /// Used car in fair condition
            /// </summary>
            Fair = 5,

            /// <summary>
            /// Used car in poor condition
            /// </summary>
            Poor = 6,

            /// <summary>
            /// Car needs major repairs
            /// </summary>
            NeedsRepair = 7
        }

        /// <summary>
        /// Represents the transmission type of a car
        /// </summary>
        public enum TransmissionType
        {
            /// <summary>
            /// Manual transmission
            /// </summary>
            Manual = 1,

            /// <summary>
            /// Automatic transmission
            /// </summary>
            Automatic = 2,

            /// <summary>
            /// Semi-automatic transmission
            /// </summary>
            SemiAutomatic = 3,

            /// <summary>
            /// Continuously Variable Transmission
            /// </summary>
            CVT = 4
        }

        /// <summary>
        /// Represents the category of a car specification
        /// </summary>
        public enum SpecificationCategory
        {
            /// <summary>
            /// Engine and performance related specifications
            /// </summary>
            Engine = 1,

            /// <summary>
            /// Exterior features and dimensions
            /// </summary>
            Exterior = 2,

            /// <summary>
            /// Interior features and comfort
            /// </summary>
            Interior = 3,

            /// <summary>
            /// Safety features
            /// </summary>
            Safety = 4,

            /// <summary>
            /// Technology and entertainment features
            /// </summary>
            Technology = 5,

            /// <summary>
            /// Fuel efficiency and environmental
            /// </summary>
            Efficiency = 6,

            /// <summary>
            /// Warranty and service information
            /// </summary>
            Warranty = 7,

            /// <summary>
            /// Other miscellaneous specifications
            /// </summary>
            Other = 8
        }

        /// <summary>
        /// Represents the data type of a specification value
        /// </summary>
        public enum SpecificationDataType
        {
            /// <summary>
            /// Text/string value
            /// </summary>
            Text = 1,

            /// <summary>
            /// Numeric value (integer or decimal)
            /// </summary>
            Number = 2,

            /// <summary>
            /// Boolean value (yes/no, true/false)
            /// </summary>
            Boolean = 3,

            /// <summary>
            /// Date value
            /// </summary>
            Date = 4,

            /// <summary>
            /// Single selection from predefined options
            /// </summary>
            SingleSelect = 5,

            /// <summary>
            /// Multiple selections from predefined options
            /// </summary>
            MultiSelect = 6,

            /// <summary>
            /// URL/link value
            /// </summary>
            Url = 7
        }
    }
}
