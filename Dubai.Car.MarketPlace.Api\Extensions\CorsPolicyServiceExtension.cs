﻿namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Adds a default CORS policy named "CorsPolicy" to the specified service collection.
    /// </summary>
    /// <remarks>The added policy allows requests from any origin, supports the HTTP methods "GET", "POST",
    /// "OPTIONS", "PUT", "PATCH", and "DELETE", and permits any headers. This method is typically used during
    /// application startup to configure CORS settings.</remarks>
    public static class CorsPolicyServiceExtension
    {
        /// <summary>
        /// AddCorsPolicyConfiguring method
        /// </summary>
        /// <param name="services"></param>
        public static void AddCorsPolicy(this IServiceCollection services)
        {
            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", builder =>
                {
                    builder.AllowAnyOrigin()
                        .WithMethods("GET", "POST", "OPTIONS", "PUT", "PATCH", "DELETE")
                        .AllowAnyHeader();
                });
            });
        }
    }
}
