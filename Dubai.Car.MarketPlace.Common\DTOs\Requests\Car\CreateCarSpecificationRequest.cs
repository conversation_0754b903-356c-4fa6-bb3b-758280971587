using System.ComponentModel.DataAnnotations;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Car
{
    /// <summary>
    /// Request DTO for creating a new car specification template
    /// </summary>
    public class CreateCarSpecificationRequest
    {
        /// <summary>
        /// The name of the specification
        /// </summary>
        [Required(ErrorMessage = "Name is required")]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = default!;

        /// <summary>
        /// The category of the specification for grouping
        /// </summary>
        [Required(ErrorMessage = "Category is required")]
        public SpecificationCategory Category { get; set; }

        /// <summary>
        /// The data type of the specification value
        /// </summary>
        [Required(ErrorMessage = "Data type is required")]
        public SpecificationDataType DataType { get; set; }

        /// <summary>
        /// The unit of measurement
        /// </summary>
        [StringLength(20, ErrorMessage = "Unit cannot exceed 20 characters")]
        public string? Unit { get; set; }

        /// <summary>
        /// The description of the specification
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Whether this specification is required for car listings
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// Whether this specification is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting specifications in UI
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Predefined options for dropdown/select specifications
        /// </summary>
        public List<string>? PredefinedOptions { get; set; }

        /// <summary>
        /// Minimum value for numeric specifications
        /// </summary>
        public decimal? MinValue { get; set; }

        /// <summary>
        /// Maximum value for numeric specifications
        /// </summary>
        public decimal? MaxValue { get; set; }

        /// <summary>
        /// Regular expression pattern for text validation
        /// </summary>
        [StringLength(200, ErrorMessage = "Validation pattern cannot exceed 200 characters")]
        public string? ValidationPattern { get; set; }

        /// <summary>
        /// Help text to display to users
        /// </summary>
        [StringLength(500, ErrorMessage = "Help text cannot exceed 500 characters")]
        public string? HelpText { get; set; }
    }

    /// <summary>
    /// Request DTO for updating a car specification template
    /// </summary>
    public class UpdateCarSpecificationRequest
    {
        /// <summary>
        /// The name of the specification
        /// </summary>
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string? Name { get; set; }

        /// <summary>
        /// The category of the specification for grouping
        /// </summary>
        public SpecificationCategory? Category { get; set; }

        /// <summary>
        /// The data type of the specification value
        /// </summary>
        public SpecificationDataType? DataType { get; set; }

        /// <summary>
        /// The unit of measurement
        /// </summary>
        [StringLength(20, ErrorMessage = "Unit cannot exceed 20 characters")]
        public string? Unit { get; set; }

        /// <summary>
        /// The description of the specification
        /// </summary>
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Whether this specification is required for car listings
        /// </summary>
        public bool? IsRequired { get; set; }

        /// <summary>
        /// Whether this specification is currently active
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Display order for sorting specifications in UI
        /// </summary>
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// Predefined options for dropdown/select specifications
        /// </summary>
        public List<string>? PredefinedOptions { get; set; }

        /// <summary>
        /// Minimum value for numeric specifications
        /// </summary>
        public decimal? MinValue { get; set; }

        /// <summary>
        /// Maximum value for numeric specifications
        /// </summary>
        public decimal? MaxValue { get; set; }

        /// <summary>
        /// Regular expression pattern for text validation
        /// </summary>
        [StringLength(200, ErrorMessage = "Validation pattern cannot exceed 200 characters")]
        public string? ValidationPattern { get; set; }

        /// <summary>
        /// Help text to display to users
        /// </summary>
        [StringLength(500, ErrorMessage = "Help text cannot exceed 500 characters")]
        public string? HelpText { get; set; }
    }
}
