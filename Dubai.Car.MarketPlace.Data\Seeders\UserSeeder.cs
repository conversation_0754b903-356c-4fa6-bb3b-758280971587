using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    public static class UserSeeder
    {
        public static async Task SeedUsersAsync(IServiceProvider serviceProvider, ILogger logger)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<CarContext>();
                var userManager = scope.ServiceProvider.GetRequiredService<UserManager<User>>();
                var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<Role>>();

                // Check if customer users already exist
                var existingCustomers = await userManager.GetUsersInRoleAsync(nameof(RoleEnums.SystemRoles.Customer));
                if (existingCustomers.Any())
                {
                    logger.LogInformation("Customer users already exist. Skipping user seeding.");
                    return;
                }

                // Ensure Customer role exists
                var customerRole = await roleManager.FindByNameAsync(nameof(RoleEnums.SystemRoles.Customer));
                if (customerRole == null)
                {
                    logger.LogWarning("Customer role not found. Creating Customer role...");
                    customerRole = new Role
                    {
                        Name = nameof(RoleEnums.SystemRoles.Customer),
                        NormalizedName = nameof(RoleEnums.SystemRoles.Customer).ToUpper(),
                        Description = "Regular customers who can browse and purchase",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    await roleManager.CreateAsync(customerRole);
                }

                // Sample customer users
                var customers = new List<(User user, string password)>
                {
                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "John",
                        LastName = "Doe",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Male",
                        UserType = AuthEnums.UserTypes.Customer,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Dubai Marina, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow
                    }, "Password123!"),

                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Sarah",
                        LastName = "Johnson",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Female",
                        UserType = AuthEnums.UserTypes.Customer,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Downtown Dubai, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow
                    }, "Password123!"),

                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Ahmed",
                        LastName = "Al-Rashid",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Male",
                        UserType = AuthEnums.UserTypes.Customer,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Business Bay, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow
                    }, "Password123!"),

                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "Maria",
                        LastName = "Garcia",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Female",
                        UserType = AuthEnums.UserTypes.Customer,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Jumeirah Beach Residence, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow
                    }, "Password123!"),

                    (new User
                    {
                        Id = Guid.NewGuid(),
                        FirstName = "David",
                        LastName = "Smith",
                        Email = "<EMAIL>",
                        UserName = "<EMAIL>",
                        NormalizedEmail = "<EMAIL>",
                        NormalizedUserName = "<EMAIL>",
                        Gender = "Male",
                        UserType = AuthEnums.UserTypes.Customer,
                        IsActive = true,
                        EmailConfirmed = true,
                        PhoneNumber = "+************",
                        Address = "Dubai Investment Park, Dubai",
                        State = "Dubai",
                        Country = "UAE",
                        CreatedOn = DateTime.UtcNow,
                        UpdatedOn = DateTime.UtcNow
                    }, "Password123!")
                };

                var createdUsersCount = 0;
                foreach (var (user, password) in customers)
                {
                    var result = await userManager.CreateAsync(user, password);
                    if (result.Succeeded)
                    {
                        // Add user to Customer role
                        await userManager.AddToRoleAsync(user, nameof(RoleEnums.SystemRoles.Customer));
                        createdUsersCount++;
                        logger.LogInformation($"Successfully created customer user: {user.Email}");
                    }
                    else
                    {
                        logger.LogWarning($"Failed to create user {user.Email}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                    }
                }

                logger.LogInformation($"Successfully seeded {createdUsersCount} customer users.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding customer users.");
                throw;
            }
        }
    }
}
