using Dubai.Car.MarketPlace.Common.DTOs.Requests.Vendor;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Security.Claims;
using WatchDog;
using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// Controller for vendor operations
    /// </summary>
    public class VendorController : BaseController
    {
        private readonly IVendorService _vendorService;
        private readonly ILogger<VendorController> _logger;

        /// <summary>
        /// Constructor for VendorController
        /// </summary>
        /// <param name="vendorService">The vendor service</param>
        /// <param name="logger">The logger</param>
        public VendorController(
            IVendorService vendorService,
            ILogger<VendorController> logger)
        {
            _vendorService = vendorService;
            _logger = logger;
        }

        #region Public Endpoints

        /// <summary>
        /// Submit a new vendor application
        /// </summary>
        /// <param name="model">The vendor application details</param>
        /// <returns>Result of the application submission</returns>
        [HttpPost("apply")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<VendorApplicationResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> SubmitApplication([FromBody] VendorApplicationRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for vendor application: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "SubmitApplication");
                return BadRequest(ModelState);
            }

            WatchLogger.Log($"Vendor application submission for {model.BusinessName}", "SubmitApplication");
            var result = await _vendorService.SubmitApplicationAsync(model);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get the status of a vendor application by ID (for public access)
        /// </summary>
        /// <param name="applicationId">The ID of the application</param>
        /// <param name="email">The business email of the vendor</param>
        /// <returns>The vendor application status</returns>
        [HttpGet("application-status/{applicationId}")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<VendorApplicationResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetApplicationStatus(Guid applicationId, [FromQuery] string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = "Email is required"
                });
            }

            WatchLogger.Log($"Vendor application status check for ID: {applicationId}, Email: {email}", "GetApplicationStatus");
            var result = await _vendorService.GetApplicationByIdAsync(applicationId);
            
            if (!result.IsSuccess)
            {
                return StatusCode((int)result.StatusCode, result);
            }
            
            // Verify the email matches the application
            if (!result.Data.BusinessEmail.Equals(email, StringComparison.OrdinalIgnoreCase))
            {
                WatchLogger.LogWarning($"Unauthorized attempt to access vendor application status. ID: {applicationId}, Email: {email}", "GetApplicationStatus");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = "Unauthorized access to application status"
                });
            }
            
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion

        #region Vendor Endpoints

        /// <summary>
        /// Get the current vendor's application
        /// </summary>
        /// <returns>The vendor's application details</returns>
        [HttpGet("my-application")]
        [Authorize(Roles = "Vendor")]
        [ProducesResponseType(typeof(ApiResponse<VendorApplicationResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetMyApplication()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out Guid userIdLong))
            {
                WatchLogger.LogWarning("Invalid user ID in token", "GetMyApplication");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = ResponseMessages.UnauthorizedResponse
                });
            }

            WatchLogger.Log($"Get own application request for vendor ID: {userIdLong}", "GetMyApplication");
            var result = await _vendorService.GetVendorOwnApplicationAsync(userIdLong);
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion

        #region Admin Endpoints

        /// <summary>
        /// Get all vendor applications with optional filtering and pagination
        /// </summary>
        /// <param name="status">Filter by application status</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated list of vendor applications</returns>
        [HttpGet("admin/applications")]
        [Authorize(Roles = "Super Admin,Content Admin")]
        [ProducesResponseType(typeof(ApiResponse<PaginatedResponseDto<VendorApplicationResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetApplications(
            [FromQuery] VendorApplicationStatus? status = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            WatchLogger.Log($"Get vendor applications request. Status: {status}, Page: {pageNumber}, Size: {pageSize}", "GetApplications");
            var result = await _vendorService.GetApplicationsAsync(status, pageNumber, pageSize);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get a specific vendor application by ID
        /// </summary>
        /// <param name="applicationId">The ID of the application</param>
        /// <returns>The vendor application details</returns>
        [HttpGet("admin/applications/{applicationId}")]
        [Authorize(Roles = "Super Admin,Content Admin")]
        [ProducesResponseType(typeof(ApiResponse<VendorApplicationResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetApplicationById(Guid applicationId)
        {
            WatchLogger.Log($"Get vendor application by ID: {applicationId}", "GetApplicationById");
            var result = await _vendorService.GetApplicationByIdAsync(applicationId);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Review a vendor application (approve, reject, or request additional information)
        /// </summary>
        /// <param name="model">The review details</param>
        /// <returns>Result of the application review</returns>
        [HttpPost("admin/applications/review")]
        [Authorize(Roles = "Super Admin")]
        [ProducesResponseType(typeof(ApiResponse<VendorApplicationResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> ReviewApplication([FromBody] VendorApplicationReviewDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for vendor application review: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "ReviewApplication");
                return BadRequest(ModelState);
            }

            var adminUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(adminUserId) || !Guid.TryParse(adminUserId, out Guid adminUserIdLong))
            {
                WatchLogger.LogWarning("Invalid admin user ID in token", "ReviewApplication");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = ResponseMessages.UnauthorizedResponse
                });
            }

            WatchLogger.Log($"Vendor application review for ID: {model.ApplicationId}, New Status: {model.NewStatus}, By Admin: {adminUserIdLong}", "ReviewApplication");
            var result = await _vendorService.ReviewApplicationAsync(adminUserIdLong, model);
            return StatusCode((int)result.StatusCode, result);
        }

        #endregion
    }
}
