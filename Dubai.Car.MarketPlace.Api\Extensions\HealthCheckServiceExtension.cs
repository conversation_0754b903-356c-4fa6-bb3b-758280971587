using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using WatchDog;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Extension methods for configuring health check services
    /// </summary>
    public static class HealthCheckServiceExtension
    {
        /// <summary>
        /// Adds health check services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <param name="connectionString">The database connection string</param>
        public static void AddHealthCheckServices(this IServiceCollection services, IConfiguration configuration, string connectionString)
        {
            // Add health checks
            services.AddHealthChecks()
                // Add a check for the database
                .AddNpgSql(
                    connectionString: connectionString,
                    name: "PostgreSQL Database",
                    failureStatus: HealthStatus.Degraded,
                    tags: new[] { "db", "postgresql", "database" })

                // Add a check for Hangfire
                .AddHangfire(
                    options =>
                    {
                        options.MinimumAvailableServers = 1;
                    },
                    name: "Hangfire",
                    failureStatus: HealthStatus.Degraded,
                    tags: new[] { "hangfire", "background", "jobs" });

            // Get the application base URL
            var baseUrl = configuration["AppConfig:BaseUrl"] ?? "http://localhost:5291";
            var healthCheckEndpoint = "/health";

            // Add health check UI using configuration
            services.AddHealthChecksUI(setupSettings: setup =>
            {
                // The UI will use the settings from the "HealthChecks-UI" section in appsettings.json
                // But we'll add some additional configuration for safety

                setup.SetEvaluationTimeInSeconds(
                    configuration.GetValue<int>("HealthChecks-UI:EvaluationTimeInSeconds", 60));

                setup.MaximumHistoryEntriesPerEndpoint(50);
                setup.SetApiMaxActiveRequests(1);

                // If no endpoints are configured in appsettings.json, add one programmatically
                if (configuration.GetSection("HealthChecks-UI:HealthChecks").Exists())
                {
                    // Use the application's own URL for health checks to avoid unspecified address errors
                    var healthCheckUrl = $"{baseUrl.TrimEnd('/')}{healthCheckEndpoint}";
                    WatchLogger.Log($"Adding health check endpoint: {healthCheckUrl}");
                    setup.AddHealthCheckEndpoint("School Payment System API", healthCheckUrl);
                }

                // Set webhook notification settings
                if (configuration.GetValue<bool>("HealthChecks-UI:DisableWebhookEndpoint", true))
                {
                    // In newer versions, we can't directly disable webhooks, but we can set them to not be used
                    setup.SetMinimumSecondsBetweenFailureNotifications(int.MaxValue);
                }

                // Configure UI settings
                setup.SetHeaderText("School Payment System Health Checks");
                setup.SetMinimumSecondsBetweenFailureNotifications(
                    configuration.GetValue<int>("HealthChecks-UI:MinimumSecondsBetweenFailureNotifications", 300));
            })
            .AddInMemoryStorage(); // Use in-memory storage for health check UI
        }

        /// <summary>
        /// Configures health check middleware
        /// </summary>
        /// <param name="app">The application builder</param>
        public static void UseHealthCheckConfiguration(this IApplicationBuilder app)
        {
            // Get configuration
            var serviceProvider = app.ApplicationServices;
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            // Map health check endpoint
            app.UseHealthChecks("/health", new HealthCheckOptions
            {
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse,
                ResultStatusCodes =
                    {
                        [HealthStatus.Healthy] = StatusCodes.Status200OK,
                        [HealthStatus.Degraded] = StatusCodes.Status200OK,
                        [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
                    }
            });

            // Map health check UI endpoint with proper configuration
            app.UseHealthChecksUI(options =>
            {
                options.UIPath = "/health-ui";
                options.ApiPath = "/health-api";

                // Configure UI settings based on environment
                if (env == "Development")
                {
                    // In production, use specific settings to avoid unspecified address errors
                    // Add custom stylesheet for better UI in production
                    options.AddCustomStylesheet("wwwroot/healthchecks.css");
                }
            });
        }
    }
}
