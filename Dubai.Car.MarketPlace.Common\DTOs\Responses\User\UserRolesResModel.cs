using System.Collections.Generic;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.User;

public class UserRolesResModel
{
    public long UserId { get; set; }
    public string UserEmail { get; set; }
    public List<UserRoleDetail> UserRoles { get; set; } = new List<UserRoleDetail>();
}

public class UserRoleDetail
{
    public long UserRoleId { get; set; }
    public long RoleId { get; set; }
    public string RoleName { get; set; }
    public long? SchoolId { get; set; }
    public string SchoolName { get; set; }
    public long? InstitutionId { get; set; }
    public string InstitutionName { get; set; }
} 