﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Car
{
    /// <summary>
    /// Request to update the status of a car.
    /// </summary>
    public class UpdateCarStatusRequest
    {
        /// <summary>
        /// New status of the car.
        /// </summary>
        [Required]
        public CarStatus Status { get; set; }
    }

    /// <summary>
    /// Request to toggle the featured state of a car.
    /// </summary>
    public class ToggleFeaturedRequest
    {
        /// <summary>
        /// Indicates whether the car should be marked as featured.
        /// </summary>
        [Required]
        public bool IsFeatured { get; set; }
    }

    /// <summary>
    /// Request DTO for toggling specification status
    /// </summary>
    public class ToggleSpecificationStatusRequest
    {
        /// <summary>
        /// Whether the specification should be active
        /// </summary>
        [Required]
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Request DTO for updating display order
    /// </summary>
    public class UpdateDisplayOrderRequest
    {
        /// <summary>
        /// Dictionary of specification ID to display order
        /// </summary>
        [Required]
        public Dictionary<Guid, int> SpecificationOrders { get; set; } = new Dictionary<Guid, int>();
    }

    /// <summary>
    /// Request DTO for validating specification value
    /// </summary>
    public class ValidateSpecificationValueRequest
    {
        /// <summary>
        /// The value to validate
        /// </summary>
        [Required]
        public string Value { get; set; } = default!;
    }
}

