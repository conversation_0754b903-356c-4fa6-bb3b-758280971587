using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Car
{
    /// <summary>
    /// Request DTO for filtering car listings
    /// </summary>
    public class CarFilterRequest
    {
        /// <summary>
        /// Search term for car model, brand, or description
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by brand IDs
        /// </summary>
        public List<Guid>? BrandIds { get; set; }

        /// <summary>
        /// Filter by body type IDs
        /// </summary>
        public List<Guid>? BodyTypeIds { get; set; }

        /// <summary>
        /// Filter by fuel type IDs
        /// </summary>
        public List<Guid>? FuelTypeIds { get; set; }

        /// <summary>
        /// Filter by vendor ID
        /// </summary>
        public Guid? VendorId { get; set; }

        /// <summary>
        /// Filter by car condition
        /// </summary>
        public List<CarCondition>? Conditions { get; set; }

        /// <summary>
        /// Filter by car status
        /// </summary>
        public List<CarStatus>? Statuses { get; set; }

        /// <summary>
        /// Filter by transmission type
        /// </summary>
        public List<TransmissionType>? TransmissionTypes { get; set; }

        /// <summary>
        /// Minimum price filter
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// Maximum price filter
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// Minimum year filter
        /// </summary>
        public int? MinYear { get; set; }

        /// <summary>
        /// Maximum year filter
        /// </summary>
        public int? MaxYear { get; set; }

        /// <summary>
        /// Minimum mileage filter
        /// </summary>
        public int? MinMileage { get; set; }

        /// <summary>
        /// Maximum mileage filter
        /// </summary>
        public int? MaxMileage { get; set; }

        /// <summary>
        /// Filter by color
        /// </summary>
        public List<string>? Colors { get; set; }

        /// <summary>
        /// Filter by number of doors
        /// </summary>
        public List<int>? NumberOfDoors { get; set; }

        /// <summary>
        /// Filter by number of seats
        /// </summary>
        public List<int>? NumberOfSeats { get; set; }

        /// <summary>
        /// Filter by featured cars only
        /// </summary>
        public bool? IsFeatured { get; set; }

        /// <summary>
        /// Filter by sold status
        /// </summary>
        public bool? IsSold { get; set; }

        /// <summary>
        /// Sort by field
        /// </summary>
        public string? SortBy { get; set; } = "CreatedOn";

        /// <summary>
        /// Sort direction (asc/desc)
        /// </summary>
        public string? SortDirection { get; set; } = "desc";

        /// <summary>
        /// Page number for pagination
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Page size for pagination
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// Include deleted cars (admin only)
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;
    }
}
