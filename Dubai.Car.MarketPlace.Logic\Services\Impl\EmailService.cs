﻿using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.Mail;
using WatchDog;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly AppSettings _appSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(
            IOptions<EmailSettings> emailSettings,
            IOptions<AppSettings> appSettings,
            ILogger<EmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _appSettings = appSettings.Value;
            _logger = logger;
        }

        #region Send Mail
        public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            try
            {
                var message = new MailMessage
                {
                    From = new MailAddress(_emailSettings.SenderEmail, _emailSettings.SenderName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = isHtml
                };

                message.To.Add(new MailAddress(to));

                using var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort)
                {
                    Credentials = new NetworkCredential(_emailSettings.SmtpUsername, _emailSettings.SmtpPassword),
                    EnableSsl = true
                };

                await client.SendMailAsync(message);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email to {EmailAddress}", to);
                WatchLogger.LogError($"Error sending email to {to}: {ex.Message}", "SendEmailAsync", ex.ToString());
                return false;
            }
        }
        #endregion

        /// <summary>
        /// Sends an account verification email to the specified recipient.
        /// </summary>
        /// <remarks>The verification email contains a link that the recipient can use to verify their
        /// email address. The link is valid for 24 hours. If the recipient did not request an account, they can safely
        /// ignore the email.</remarks>
        /// <param name="to">The email address of the recipient. This parameter cannot be null or empty.</param>
        /// <param name="verificationToken">The unique token used to verify the recipient's email address. This parameter cannot be null or empty.</param>
        /// <param name="firstName">The first name of the recipient, used to personalize the email. This parameter cannot be null or empty.</param>
        /// <returns>A task that represents the asynchronous operation. The task result is <see langword="true"/> if the email
        /// was sent successfully; otherwise, <see langword="false"/>.</returns>
        public async Task<bool> SendAccountVerificationEmailAsync(string? to, string verificationToken, string firstName)
        {
            var verificationLink = $"{_appSettings.AppUrl}/email-verify?token={verificationToken}&email={WebUtility.UrlEncode(to)}";

            WatchLogger.Log($"Generated verification link for {to}", "SendAccountVerificationEmailAsync");

            var subject = "Verify Your Auto Market Account";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Hello {firstName},</h2>
                        <p>Thank you for registering with <strong>Auto Market</strong>. Please verify your email address by clicking the button below:</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{verificationLink}' style='background-color: #4a6ee0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Verify Email Address</a>
                        </div>
                        <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
                        <p style='word-break: break-all;'>{verificationLink}</p>
                        <p>This link will expire in 24 hours.</p>
                        <p>If you didn't create an account, you can safely ignore this email.</p>
                        <p>Best regards,<br>The Psyvids Team</p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(to, subject, body);
        }        public async Task<bool> SendPasswordResetEmailAsync(string to, string resetToken, string firstName)
        {
            var resetLink = $"{_appSettings.AppUrl}/api/auth/reset-password?token={resetToken}&email={WebUtility.UrlEncode(to)}";

            WatchLogger.Log($"Generated password reset link for {to}", "SendPasswordResetEmailAsync");

            var subject = "Reset Your Psyvids Password";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Hello {firstName},</h2>
                        <p>We received a request to reset your password. Click the button below to create a new password:</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{resetLink}' style='background-color: #4a6ee0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Reset Password</a>
                        </div>
                        <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
                        <p style='word-break: break-all;'>{resetLink}</p>
                        <p>This link will expire in 24 hours.</p>
                        <p>If you didn't request a password reset, you can safely ignore this email.</p>
                        <p>Best regards,<br>The Psyvids Team</p>
                    </div>
                </body>
                </html>";

            return await SendEmailAsync(to, subject, body);
        }

        /// <inheritdoc/>
        public async Task<bool> SendVendorApplicationConfirmationEmailAsync(string to, string businessName, Guid applicationId)
        {
            var statusCheckUrl = $"{_appSettings.AppUrl}/vendor/application-status/{applicationId}?email={WebUtility.UrlEncode(to)}";
            
            WatchLogger.Log($"Generated vendor application confirmation for {to}", "SendVendorApplicationConfirmationEmailAsync");

            var subject = "Dubai Car Marketplace - Vendor Application Received";
            
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Hello {businessName},</h2>
                        <p>Thank you for applying to become a vendor on Dubai Car Marketplace!</p>
                        <p>We have received your application and it is currently under review. Our team will carefully assess your information to ensure it meets our quality standards.</p>
                        <h3>What happens next?</h3>
                        <ol>
                            <li>Our team will review your application (typically within 2-3 business days)</li>
                            <li>You may be contacted if additional information is needed</li>
                            <li>Once approved, you will receive login credentials to your vendor dashboard</li>
                            <li>You can then choose a subscription plan and start listing your inventory</li>
                        </ol>
                        <p>You can check the status of your application at any time using the link below:</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{statusCheckUrl}' style='background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Check Application Status</a>
                        </div>
                        <p>If you have any questions or need assistance, please contact our support <NAME_EMAIL></p>
                        <p>Best regards,<br>Dubai Car Marketplace Team</p>
                    </div>
                </body>
                </html>";
            
            return await SendEmailAsync(to, subject, body);
        }
        
        /// <inheritdoc/>
        public async Task<bool> SendVendorApprovalEmailAsync(string to, string businessName, string username, string password)
        {
            var loginUrl = $"{_appSettings.AppUrl}/login";
            
            WatchLogger.Log($"Generated vendor approval email for {to}", "SendVendorApprovalEmailAsync");

            var subject = "Dubai Car Marketplace - Vendor Application Approved";
            
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Congratulations, {businessName}!</h2>
                        <p>We are pleased to inform you that your application to become a vendor on Dubai Car Marketplace has been <strong>APPROVED</strong>!</p>
                        <h3>Your Account Details</h3>
                        <div style='background-color: #f2f2f2; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                            <p><strong>Username:</strong> {username}</p>
                            <p><strong>Temporary Password:</strong> {password}</p>
                        </div>
                        <p><strong>Important:</strong> Please change your password after your first login for security reasons.</p>
                        <h3>Next Steps</h3>
                        <ol>
                            <li>Log in to your account <a href='{loginUrl}' style='color: #4a6ee0;'>here</a></li>
                            <li>Complete your vendor profile</li>
                            <li>Select a subscription plan that suits your business needs</li>
                            <li>Set up your payment method</li>
                            <li>Start listing your vehicles and growing your business!</li>
                        </ol>
                        <p>Our support team is always available to assist you as you get started.</p>
                        <p>We look forward to a successful partnership!</p>
                        <p>Best regards,<br>Dubai Car Marketplace Team</p>
                    </div>
                </body>
                </html>";
            
            return await SendEmailAsync(to, subject, body);
        }

        public async Task<bool> SendVendorUnderReviewEmailAsync(string to, string businessName, string? rejectionReason)
        {
            var loginUrl = $"{_appSettings.AppUrl}/login";

            WatchLogger.Log($"Generated vendor approval email for {to}", "SendVendorApprovalEmailAsync");

            var subject = "Dubai Car Marketplace - Vendor Application Under Review";

            var body = $@"
            <html>
            <body style='font-family: Arial, sans-serif; color: #333;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #4a6ee0;'>Hello, {businessName}</h2>
                    <p>We would like to inform you that your application to become a vendor on Dubai Car Marketplace is currently <strong>under review</strong>.</p>
                    <p>Our team is carefully evaluating your submission and will notify you as soon as the review process is complete.</p>
                    <p>We appreciate your patience and interest in partnering with us.</p>
                    <p>Best regards,<br>Dubai Car Marketplace Team</p>
                </div>
            </body>
            </html>";

            return await SendEmailAsync(to, subject, body);
        }

        /// <inheritdoc/>
        public async Task<bool> SendVendorRejectionEmailAsync(string to, string businessName, string? rejectionReason)
        {
            WatchLogger.Log($"Generated vendor rejection email for {to}", "SendVendorRejectionEmailAsync");

            var subject = "Dubai Car Marketplace - Vendor Application Status";
            
            var reasonSection = string.IsNullOrEmpty(rejectionReason) ? "" : $@"
                <h3>Reason</h3>
                <p>{rejectionReason}</p>
            ";
            
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Hello {businessName},</h2>
                        <p>Thank you for your interest in becoming a vendor on Dubai Car Marketplace.</p>
                        <p>After careful consideration, we regret to inform you that we are unable to approve your vendor application at this time.</p>
                        {reasonSection}
                        <p>If you believe this decision was made in error or if you have additional information that might affect our decision, please feel free to contact our support <NAME_EMAIL></p>
                        <p>You may also submit a new application after addressing the concerns mentioned above.</p>
                        <p>We wish you the best in your future endeavors.</p>
                        <p>Best regards,<br>Dubai Car Marketplace Team</p>
                    </div>
                </body>
                </html>";
            
            return await SendEmailAsync(to, subject, body);
        }
        
        /// <inheritdoc/>
        public async Task<bool> SendAdditionalInfoRequestEmailAsync(string to, string businessName, string requestDetails)
        {
            WatchLogger.Log($"Generated additional info request email for {to}", "SendAdditionalInfoRequestEmailAsync");

            var subject = "Dubai Car Marketplace - Additional Information Required";
            
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Hello {businessName},</h2>
                        <p>Thank you for your vendor application to Dubai Car Marketplace.</p>
                        <p>We have reviewed your application and require some additional information before we can proceed with the approval process.</p>
                        <h3>Information Needed</h3>
                        <div style='background-color: #f9f9f9; padding: 15px; border-left: 4px solid #4a6ee0; margin: 20px 0;'>
                            <p>{requestDetails}</p>
                        </div>
                        <p>Please reply to this email with the requested information or documents as soon as possible so that we can continue processing your application.</p>
                        <p>If you have any questions or need clarification, please don't hesitate to contact our support <NAME_EMAIL></p>
                        <p>Best regards,<br>Dubai Car Marketplace Team</p>
                    </div>
                </body>
                </html>";
            
            return await SendEmailAsync(to, subject, body);
        }
        
        /// <inheritdoc/>
        public async Task<bool> SendAdminWelcomeEmailAsync(string to, string firstName, string lastName, string username, string temporaryPassword, string roleName)
        {
            var loginUrl = $"{_appSettings.AppUrl}/admin/login";
            
            WatchLogger.Log($"Generated admin welcome email for {to}", "SendAdminWelcomeEmailAsync");

            var subject = "Welcome to Dubai Car Marketplace - Admin Account Created";
            
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #4a6ee0;'>Welcome to Dubai Car Marketplace, {firstName} {lastName}!</h2>
                        <p>Your admin account has been successfully created on Dubai Car Marketplace.</p>
                        
                        <h3>Your Account Details</h3>
                        <div style='background-color: #f2f2f2; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                            <p><strong>Email/Username:</strong> {username}</p>
                            <p><strong>Temporary Password:</strong> {temporaryPassword}</p>
                            <p><strong>Role:</strong> {roleName}</p>
                        </div>
                        
                        <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                            <p style='margin: 0; color: #856404;'><strong>⚠️ Important Security Notice:</strong></p>
                            <p style='margin: 5px 0 0 0; color: #856404;'>Please change your password immediately after your first login for security reasons.</p>
                        </div>
                        
                        <h3>Getting Started</h3>
                        <ol>
                            <li>Click the login button below to access the admin dashboard</li>
                            <li>Use your temporary credentials to log in</li>
                            <li>Change your password in your profile settings</li>
                            <li>Complete your admin profile information</li>
                            <li>Familiarize yourself with your role permissions and responsibilities</li>
                        </ol>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{loginUrl}' style='background-color: #4a6ee0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;'>Access Admin Dashboard</a>
                        </div>
                        
                        <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
                        <p style='word-break: break-all; color: #4a6ee0;'>{loginUrl}</p>
                        
                        <p>If you have any questions or need assistance getting started, please don't hesitate to contact the system administrator or our support team.</p>
                        
                        <p>Best regards,<br>Dubai Car Marketplace Team</p>
                    </div>
                </body>
                </html>";
            
            return await SendEmailAsync(to, subject, body);
        }
    }
}
