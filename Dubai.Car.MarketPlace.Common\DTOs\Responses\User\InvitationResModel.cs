namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.User;

/// <summary>
/// Response model for user invitation
/// </summary>
public class InvitationResModel
{
    /// <summary>
    /// Email address of the invited user
    /// </summary>
    public string Email { get; set; }
    
    /// <summary>
    /// First name of the invited user
    /// </summary>
    public string FirstName { get; set; }
    
    /// <summary>
    /// Last name of the invited user
    /// </summary>
    public string LastName { get; set; }
    
    /// <summary>
    /// The role assigned to the invited user
    /// </summary>
    public string Role { get; set; }
    
    /// <summary>
    /// Invitation token to be used for registration
    /// </summary>
    public string InvitationToken { get; set; }
    
    /// <summary>
    /// Date and time when the invitation expires
    /// </summary>
    public DateTime ExpiresAt { get; set; }
} 