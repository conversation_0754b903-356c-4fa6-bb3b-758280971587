﻿namespace Dubai.Car.MarketPlace.Common.Configurations
{
    public class AwsSettings
    {
        public string AccessKey { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public string Region { get; set; } = string.Empty;
        public string BucketName { get; set; } = string.Empty;
        public int PresignedUrlExpiryMinutes { get; set; } = 60;
        public int MaxFileSizeMB { get; set; } = 10; // Default max file size: 10MB
    }
}
