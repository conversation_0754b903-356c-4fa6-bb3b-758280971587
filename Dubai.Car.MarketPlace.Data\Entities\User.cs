﻿using Microsoft.AspNetCore.Identity;
using static Dubai.Car.MarketPlace.Common.Enums.AuthEnums;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a user in the system.
    /// </summary>
    public class User : IdentityUser<Guid>
    {
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public string? MiddleName { get; set; }
        public string? Gender { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsProfileCompleted { get; set; } = false;
        public override string NormalizedEmail => Email?.ToUpper();
        public override string UserName => Email;
        public override string NormalizedUserName => UserName.ToUpper();
        public override bool EmailConfirmed { get; set; } = false;
        public override bool PhoneNumberConfirmed { get; set; } = false;
        public override bool TwoFactorEnabled { get; set; } = false;
        public override bool LockoutEnabled { get; set; } = false;
        public override int AccessFailedCount { get; set; } = 0;
        public string? Address { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? Designation { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? RefreshTokenExpiryTime { get; set; }
        public string? ProfilePictureKey { get; set; }
        public UserTypes UserType { get; set; } = UserTypes.Customer;

        // UAE PASS Integration Properties
        public string? EmiratesId { get; set; }
        public string? UaePassUserId { get; set; }
        public string? UaePassUuid { get; set; }
        public bool IsUaePassVerified { get; set; } = false;
        public DateTime? UaePassLastLogin { get; set; }

        // Audit Properties
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
    }
}
