using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Service for monitoring the health of the application
    /// </summary>
    public class HealthCheckMonitoringService
    {
        private readonly ILogger<HealthCheckMonitoringService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IWebHostEnvironment _env;

        /// <summary>
        /// Constructor for HealthCheckMonitoringService
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="configuration">Configuration instance</param>
        /// <param name="httpClientFactory">HTTP client factory</param>
        /// <param name="env">Web host environment</param>
        public HealthCheckMonitoringService(
            ILogger<HealthCheckMonitoringService> logger,
            IConfiguration configuration,
            IHttpClientFactory httpClientFactory,
            IWebHostEnvironment env)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _env = env;
        }

        /// <summary>
        /// Performs a health check by calling the health check endpoint
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task PerformHealthCheck()
        {
            try
            {
                var env = _env;
                _logger.LogInformation($"Performing scheduled health check at {DateTime.UtcNow}:Env {env}");

                // Use the application's own URL for health checks
                var healthCheckUrl = env.IsDevelopment() ? "http://localhost:5291/health" : "https://schoolpaymetsystem.onrender.com/health";

                var httpClient = _httpClientFactory.CreateClient("HealthCheck");
                var response = await httpClient.GetAsync(healthCheckUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Health check successful: {Response}", content);
                }
                else
                {
                    _logger.LogWarning("Health check failed with status code {StatusCode}", response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing health check");
            }
        }
    }
}
