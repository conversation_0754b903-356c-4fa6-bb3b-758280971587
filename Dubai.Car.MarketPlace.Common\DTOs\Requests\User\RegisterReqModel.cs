﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.User
{
    public class RegisterReqModel
    {
        private string _firstName { get; set; } = default!;
        public string FirstName
        {
            get => _firstName;
            set => _firstName = char.ToUpper(value[0]) + value.Substring(1);
        }

        private string _lastName { get; set; } = default!;
        public string LastName
        {
            get => _lastName;
            set => _lastName = char.ToUpper(value[0]) + value.Substring(1);
        }

        private string? _middleName { get; set; }
        public string? MiddleName
        {
            get => _middleName;
            set => _middleName = value == null ? null : char.ToUpper(value[0]) + value.Substring(1);
        }

        [Phone]
        public string PhoneNumber { get; set; } = default!;

        public string Gender { get; set; } = default!;

        [EmailAddress]
        private string _email { get; set; } = default!;
        public string Email
        {
            get => _email;
            set => _email = value.ToLower();
        }

        [DataType(DataType.Password)]
        public string Password { get; set; } = default!;

        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "Password and Confirm Password must match.")]
        public string ConfirmPassword { get; set; } = default!;

        public string? State { get; set; }
        public string Country { get; set; } = default!;


        [JsonIgnore]
        public bool IsUserInvited { get; set; } = false;
    }
}
