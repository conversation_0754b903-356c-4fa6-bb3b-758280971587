﻿using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.AspNetCore.Identity;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Class to add identity configuration
    /// </summary>
    public static class IdentityServiceExtension
    {
        /// <summary>
        /// Identity options are set here
        /// </summary>
        /// <param name="services"></param>
        public static void AddIdentityServiceConfig(this IServiceCollection services)
        {
            services.AddIdentity<User, Role>()
                .AddEntityFrameworkStores<CarContext>()
                .AddDefaultTokenProviders();

            //services.Configure<IdentityOptions>(options =>
            //{
            //    options.Password.RequireDigit = false;
            //    options.Password.RequireLowercase = false;
            //    options.Password.RequireNonAlphanumeric = false;
            //    options.Password.RequireUppercase = false;
            //    options.Password.RequiredLength = 6;
            //    options.User.RequireUniqueEmail = true;
            //});
        }
    }
}
