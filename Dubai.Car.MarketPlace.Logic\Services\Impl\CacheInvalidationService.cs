using System.Threading.Tasks;
using Dubai.Car.MarketPlace.Logic.Constants;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl;

/// <summary>
/// Service to handle cache invalidation patterns
/// </summary>
public class CacheInvalidationService
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<CacheInvalidationService> _logger;

    public CacheInvalidationService(
        ICacheService cacheService,
        ILogger<CacheInvalidationService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// Invalidates all role and permission caches for a user
    /// </summary>
    public async Task InvalidateUserAuthCachesAsync(long userId, string? role = null)
    {
        await _cacheService.RemoveAsync(CacheKeys.UserRoles(userId));
        await _cacheService.RemoveAsync(CacheKeys.UserPermissions(userId));
        
        if (!string.IsNullOrEmpty(role))
        {
            await _cacheService.RemoveAsync(CacheKeys.RolePermissions(role));
        }
    }
} 