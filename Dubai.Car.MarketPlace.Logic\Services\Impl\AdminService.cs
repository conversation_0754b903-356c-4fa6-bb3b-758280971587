using AutoMapper;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Net;
using WatchDog;
using static Dubai.Car.MarketPlace.Common.Enums.AuthEnums;
using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Implementation of admin-related operations
    /// </summary>
    public class AdminService : IAdminService
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly CarContext _dbContext;
        private readonly IMapper _mapper;
        private readonly ILogger<AdminService> _logger;
        private readonly IEmailService _emailService;

        /// <summary>
        /// Constructor for AdminService
        /// </summary>
        public AdminService(
            UserManager<User> userManager,
            RoleManager<Role> roleManager,
            CarContext dbContext,
            IMapper mapper,
            ILogger<AdminService> logger,
            IEmailService emailService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _dbContext = dbContext;
            _mapper = mapper;
            _logger = logger;
            _emailService = emailService;
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<AdminUserResponseDto>> CreateAdminUserAsync(CreateAdminUserRequestDto model)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null)
                {
                    WatchLogger.LogWarning($"Admin user with email {model.Email} already exists", "CreateAdminUserAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed("Admin user with this email already exists", HttpStatusCode.Conflict);
                }

                // Check if role exists
                var role = await _roleManager.FindByIdAsync(model.RoleId.ToString());
                if (role == null)
                {
                    WatchLogger.LogWarning($"Role with ID {model.RoleId} not found", "CreateAdminUserAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed("Role not found", HttpStatusCode.NotFound);
                }

                // Create new user
                var user = new User
                {
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    UserName = model.Email,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber,
                    Gender = model.Gender,
                    Country = model.Country,
                    State = model.State,
                    UserType = UserTypes.Admin,
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow,
                    EmailConfirmed = true // Admin users are pre-confirmed
                };

                var result = await _userManager.CreateAsync(user, model.TemporaryPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    WatchLogger.LogWarning($"Failed to create admin user: {errors}", "CreateAdminUserAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed($"Failed to create admin user: {errors}", HttpStatusCode.BadRequest);
                }

                // Assign role to user
                await _userManager.AddToRoleAsync(user, role.Name!);

                // Send welcome email with temporary credentials
                var emailSent = await _emailService.SendAdminWelcomeEmailAsync(
                    user.Email!, 
                    user.FirstName, 
                    user.LastName, 
                    user.Email!, 
                    model.TemporaryPassword, 
                    role.Name!);

                if (!emailSent)
                {
                    _logger.LogWarning("Failed to send welcome email to admin user: {Email}", user.Email);
                    WatchLogger.LogWarning($"Failed to send welcome email to admin user: {user.Email}", "CreateAdminUserAsync");
                    // Note: We don't fail the admin creation if email fails, just log the warning
                }

                // Map to response DTO
                var response = _mapper.Map<AdminUserResponseDto>(user);
                response.Role = role.Name!;
                response.RoleId = role.Id;
                response.IsLocked = user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow;

                WatchLogger.Log($"Admin user created successfully: {user.Email}", "CreateAdminUserAsync");
                return ApiResponse<AdminUserResponseDto>.Success(response, "Admin user created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating admin user");
                WatchLogger.LogError($"Error creating admin user: {ex.Message}", "CreateAdminUserAsync", ex.ToString());
                return ApiResponse<AdminUserResponseDto>.Failed("An error occurred while creating the admin user", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<AdminUserResponseDto>> UpdateAdminUserAsync(Guid userId, UpdateAdminUserRequestDto model)
        {
            try
            {
                // Find user
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null || user.UserType != UserTypes.Admin)
                {
                    WatchLogger.LogWarning($"Admin user with ID {userId} not found", "UpdateAdminUserAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed("Admin user not found", HttpStatusCode.NotFound);
                }

                // Check if role exists
                var role = await _roleManager.FindByIdAsync(model.RoleId.ToString());
                if (role == null)
                {
                    WatchLogger.LogWarning($"Role with ID {model.RoleId} not found", "UpdateAdminUserAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed("Role not found", HttpStatusCode.NotFound);
                }

                // Update user properties
                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.PhoneNumber = model.PhoneNumber;
                user.Gender = model.Gender;
                user.Country = model.Country;
                user.State = model.State;
                user.IsActive = model.IsActive;
                user.UpdatedOn = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    WatchLogger.LogWarning($"Failed to update admin user: {errors}", "UpdateAdminUserAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed($"Failed to update admin user: {errors}", HttpStatusCode.BadRequest);
                }

                // Update role if changed
                var currentRoles = await _userManager.GetRolesAsync(user);
                if (currentRoles.Any() && !currentRoles.Contains(role.Name!))
                {
                    await _userManager.RemoveFromRolesAsync(user, currentRoles);
                    await _userManager.AddToRoleAsync(user, role.Name!);
                }

                // Map to response DTO
                var response = _mapper.Map<AdminUserResponseDto>(user);
                response.Role = role.Name!;
                response.RoleId = role.Id;
                response.IsLocked = user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow;

                WatchLogger.Log($"Admin user updated successfully: {user.Email}", "UpdateAdminUserAsync");
                return ApiResponse<AdminUserResponseDto>.Success(response, "Admin user updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating admin user");
                WatchLogger.LogError($"Error updating admin user: {ex.Message}", "UpdateAdminUserAsync", ex.ToString());
                return ApiResponse<AdminUserResponseDto>.Failed("An error occurred while updating the admin user", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<bool>> DeleteAdminUserAsync(Guid userId)
        {
            try
            {
                // Find user
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null || user.UserType != UserTypes.Admin)
                {
                    WatchLogger.LogWarning($"Admin user with ID {userId} not found", "DeleteAdminUserAsync");
                    return ApiResponse<bool>.Failed("Admin user not found", HttpStatusCode.NotFound);
                }

                // Check if user is super admin (prevent deletion of super admin)
                var userRoles = await _userManager.GetRolesAsync(user);
                if (userRoles.Contains("Super Admin"))
                {
                    WatchLogger.LogWarning($"Cannot delete super admin user: {user.Email}", "DeleteAdminUserAsync");
                    return ApiResponse<bool>.Failed("Cannot delete super admin user", HttpStatusCode.Forbidden);
                }

                var result = await _userManager.DeleteAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    WatchLogger.LogWarning($"Failed to delete admin user: {errors}", "DeleteAdminUserAsync");
                    return ApiResponse<bool>.Failed($"Failed to delete admin user: {errors}", HttpStatusCode.BadRequest);
                }

                WatchLogger.Log($"Admin user deleted successfully: {user.Email}", "DeleteAdminUserAsync");
                return ApiResponse<bool>.Success(true, "Admin user deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting admin user");
                WatchLogger.LogError($"Error deleting admin user: {ex.Message}", "DeleteAdminUserAsync", ex.ToString());
                return ApiResponse<bool>.Failed("An error occurred while deleting the admin user", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<AdminUserResponseDto>> GetAdminUserByIdAsync(Guid userId)
        {
            try
            {
                // Find user
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null || user.UserType != UserTypes.Admin)
                {
                    WatchLogger.LogWarning($"Admin user with ID {userId} not found", "GetAdminUserByIdAsync");
                    return ApiResponse<AdminUserResponseDto>.Failed("Admin user not found", HttpStatusCode.NotFound);
                }

                // Get user roles
                var userRoles = await _userManager.GetRolesAsync(user);
                var role = userRoles.FirstOrDefault();
                var roleEntity = role != null ? await _roleManager.FindByNameAsync(role) : null;

                // Map to response DTO
                var response = _mapper.Map<AdminUserResponseDto>(user);
                response.Role = role ?? string.Empty;
                response.RoleId = roleEntity?.Id ?? Guid.Empty;
                response.IsLocked = user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow;

                return ApiResponse<AdminUserResponseDto>.Success(response, "Admin user retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving admin user");
                WatchLogger.LogError($"Error retrieving admin user: {ex.Message}", "GetAdminUserByIdAsync", ex.ToString());
                return ApiResponse<AdminUserResponseDto>.Failed("An error occurred while retrieving the admin user", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<List<AdminUserResponseDto>>> GetAllAdminUsersAsync(int page = 1, int pageSize = 10)
        {
            try
            {
                var skip = (page - 1) * pageSize;

                // Get all admin users
                var adminUsers = await _userManager.Users
                    .Where(u => u.UserType == UserTypes.Admin)
                    .OrderBy(u => u.FirstName)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                var response = new List<AdminUserResponseDto>();

                foreach (var user in adminUsers)
                {
                    // Get user roles
                    var userRoles = await _userManager.GetRolesAsync(user);
                    var role = userRoles.FirstOrDefault();
                    var roleEntity = role != null ? await _roleManager.FindByNameAsync(role) : null;

                    // Map to response DTO
                    var adminUserDto = _mapper.Map<AdminUserResponseDto>(user);
                    adminUserDto.Role = role ?? string.Empty;
                    adminUserDto.RoleId = roleEntity?.Id ?? Guid.Empty;
                    adminUserDto.IsLocked = user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow;

                    response.Add(adminUserDto);
                }

                return ApiResponse<List<AdminUserResponseDto>>.Success(response, "Admin users retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving admin users");
                WatchLogger.LogError($"Error retrieving admin users: {ex.Message}", "GetAllAdminUsersAsync", ex.ToString());
                return ApiResponse<List<AdminUserResponseDto>>.Failed("An error occurred while retrieving admin users", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<bool>> LockUnlockAdminUserAsync(Guid userId, LockAdminUserRequestDto model)
        {
            try
            {
                // Find user
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null || user.UserType != UserTypes.Admin)
                {
                    WatchLogger.LogWarning($"Admin user with ID {userId} not found", "LockUnlockAdminUserAsync");
                    return ApiResponse<bool>.Failed("Admin user not found", HttpStatusCode.NotFound);
                }

                // Check if user is super admin (prevent locking super admin)
                var userRoles = await _userManager.GetRolesAsync(user);
                if (userRoles.Contains("Super Admin"))
                {
                    WatchLogger.LogWarning($"Cannot lock super admin user: {user.Email}", "LockUnlockAdminUserAsync");
                    return ApiResponse<bool>.Failed("Cannot lock super admin user", HttpStatusCode.Forbidden);
                }

                // Set lockout
                var lockoutEnd = model.IsLocked ? DateTimeOffset.UtcNow.AddYears(100) : (DateTimeOffset?)null;
                var result = await _userManager.SetLockoutEndDateAsync(user, lockoutEnd);

                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    WatchLogger.LogWarning($"Failed to lock/unlock admin user: {errors}", "LockUnlockAdminUserAsync");
                    return ApiResponse<bool>.Failed($"Failed to lock/unlock admin user: {errors}", HttpStatusCode.BadRequest);
                }

                var action = model.IsLocked ? "locked" : "unlocked";
                WatchLogger.Log($"Admin user {action} successfully: {user.Email}", "LockUnlockAdminUserAsync");
                return ApiResponse<bool>.Success(true, $"Admin user {action} successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error locking/unlocking admin user");
                WatchLogger.LogError($"Error locking/unlocking admin user: {ex.Message}", "LockUnlockAdminUserAsync", ex.ToString());
                return ApiResponse<bool>.Failed("An error occurred while locking/unlocking the admin user", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public Task<ApiResponse<List<PermissionResponseDto>>> GetAllPermissionsAsync()
        {
            try
            {
                var permissions = new List<PermissionResponseDto>();

                // Get all permissions from the enum
                var permissionValues = Enum.GetValues<SystemPermissions>();
                foreach (var permission in permissionValues)
                {
                    var description = GetEnumDescription(permission);
                    permissions.Add(new PermissionResponseDto
                    {
                        Id = (int)permission,
                        Name = permission.ToString(),
                        Description = description,
                        Permission = permission
                    });
                }

                return Task.FromResult(ApiResponse<List<PermissionResponseDto>>.Success(permissions, "Permissions retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving permissions");
                WatchLogger.LogError($"Error retrieving permissions: {ex.Message}", "GetAllPermissionsAsync", ex.ToString());
                return Task.FromResult(ApiResponse<List<PermissionResponseDto>>.Failed("An error occurred while retrieving permissions", HttpStatusCode.InternalServerError));
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<bool>> AssignRolePermissionsAsync(AssignRolePermissionsRequestDto model)
        {
            try
            {
                // Find role
                var role = await _roleManager.FindByIdAsync(model.RoleId.ToString());
                if (role == null)
                {
                    WatchLogger.LogWarning($"Role with ID {model.RoleId} not found", "AssignRolePermissionsAsync");
                    return ApiResponse<bool>.Failed("Role not found", HttpStatusCode.NotFound);
                }

                // Remove existing permissions
                var existingPermissions = await _dbContext.RolePermissions
                    .Where(rp => rp.RoleId == model.RoleId)
                    .ToListAsync();

                _dbContext.RolePermissions.RemoveRange(existingPermissions);

                // Add new permissions
                var newPermissions = new List<RolePermission>();
                foreach (var permissionId in model.PermissionIds)
                {
                    // Find or create permission
                    var permissionEnum = (SystemPermissions)permissionId;
                    var permission = await _dbContext.Permissions
                        .FirstOrDefaultAsync(p => p.Name == permissionEnum.ToString());
                    
                    if (permission == null)
                    {
                        permission = new Permission
                        {
                            Name = permissionEnum.ToString(),
                            Description = GetEnumDescription(permissionEnum)
                        };
                        await _dbContext.Permissions.AddAsync(permission);
                        await _dbContext.SaveChangesAsync();
                    }

                    newPermissions.Add(new RolePermission
                    {
                        RoleId = model.RoleId,
                        PermissionId = permission.Id
                    });
                }

                await _dbContext.RolePermissions.AddRangeAsync(newPermissions);
                await _dbContext.SaveChangesAsync();

                WatchLogger.Log($"Role permissions assigned successfully for role: {role.Name}", "AssignRolePermissionsAsync");
                return ApiResponse<bool>.Success(true, "Role permissions assigned successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning role permissions");
                WatchLogger.LogError($"Error assigning role permissions: {ex.Message}", "AssignRolePermissionsAsync", ex.ToString());
                return ApiResponse<bool>.Failed("An error occurred while assigning role permissions", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<List<RoleWithPermissionsResponseDto>>> GetAllRolesWithPermissionsAsync()
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                var response = new List<RoleWithPermissionsResponseDto>();

                foreach (var role in roles)
                {
                    // Get role permissions
                    var rolePermissions = await _dbContext.RolePermissions
                        .Include(rp => rp.Permission)
                        .Where(rp => rp.RoleId == role.Id)
                        .ToListAsync();

                    var permissions = rolePermissions.Select(rp =>
                    {
                        if (Enum.TryParse<SystemPermissions>(rp.Permission.Name, out var permission))
                        {
                            return permission;
                        }
                        return (SystemPermissions)0; // Default value if parsing fails
                    }).Where(p => p != 0).ToList();

                    var roleDto = new RoleWithPermissionsResponseDto
                    {
                        Id = role.Id,
                        Name = role.Name!,
                        Description = role.Description,
                        IsEditable = role.IsEditable,
                        CreatedAt = role.CreatedAt,
                        UpdatedAt = role.UpdatedAt,
                        Permissions = permissions
                    };

                    response.Add(roleDto);
                }

                return ApiResponse<List<RoleWithPermissionsResponseDto>>.Success(response, "Roles with permissions retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving roles with permissions");
                WatchLogger.LogError($"Error retrieving roles with permissions: {ex.Message}", "GetAllRolesWithPermissionsAsync", ex.ToString());
                return ApiResponse<List<RoleWithPermissionsResponseDto>>.Failed("An error occurred while retrieving roles with permissions", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<RoleWithPermissionsResponseDto>> GetRoleByIdWithPermissionsAsync(Guid roleId)
        {
            try
            {
                // Find role
                var role = await _roleManager.FindByIdAsync(roleId.ToString());
                if (role == null)
                {
                    WatchLogger.LogWarning($"Role with ID {roleId} not found", "GetRoleByIdWithPermissionsAsync");
                    return ApiResponse<RoleWithPermissionsResponseDto>.Failed("Role not found", HttpStatusCode.NotFound);
                }

                // Get role permissions
                var rolePermissions = await _dbContext.RolePermissions
                    .Include(rp => rp.Permission)
                    .Where(rp => rp.RoleId == roleId)
                    .ToListAsync();

                var permissions = rolePermissions.Select(rp =>
                {
                    if (Enum.TryParse<SystemPermissions>(rp.Permission.Name, out var permission))
                    {
                        return permission;
                    }
                    return (SystemPermissions)0; // Default value if parsing fails
                }).Where(p => p != 0).ToList();

                var response = new RoleWithPermissionsResponseDto
                {
                    Id = role.Id,
                    Name = role.Name!,
                    Description = role.Description,
                    IsEditable = role.IsEditable,
                    CreatedAt = role.CreatedAt,
                    UpdatedAt = role.UpdatedAt,
                    Permissions = permissions
                };

                return ApiResponse<RoleWithPermissionsResponseDto>.Success(response, "Role with permissions retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving role with permissions");
                WatchLogger.LogError($"Error retrieving role with permissions: {ex.Message}", "GetRoleByIdWithPermissionsAsync", ex.ToString());
                return ApiResponse<RoleWithPermissionsResponseDto>.Failed("An error occurred while retrieving role with permissions", HttpStatusCode.InternalServerError);
            }
        }

        /// <summary>
        /// Gets the description attribute value from an enum
        /// </summary>
        /// <param name="enumValue">The enum value</param>
        /// <returns>Description or enum name if no description attribute</returns>
        private static string GetEnumDescription(Enum enumValue)
        {
            var field = enumValue.GetType().GetField(enumValue.ToString());
            if (field != null)
            {
                var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
                return attribute?.Description ?? enumValue.ToString();
            }
            return enumValue.ToString();
        }

        #region Dashboard Methods

        /// <inheritdoc/>
        public async Task<ApiResponse<DashboardResponseDto>> GetDashboardDataAsync()
        {
            try
            {
                WatchLogger.Log("Getting complete dashboard data", "GetDashboardDataAsync");

                var stats = await GetDashboardStatsInternalAsync();
                var pendingVendorApplications = await GetPendingVendorApplicationsInternalAsync(5);
                var supportTickets = await GetSupportTicketsDataInternalAsync();
                var revenue = await GetPlatformRevenueDataInternalAsync();

                var response = new DashboardResponseDto
                {
                    Stats = stats,
                    PendingVendorApplications = pendingVendorApplications,
                    SupportTickets = supportTickets,
                    Revenue = revenue
                };

                return ApiResponse<DashboardResponseDto>.Success(response, "Dashboard data retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dashboard data");
                WatchLogger.LogError($"Error retrieving dashboard data: {ex.Message}", "GetDashboardDataAsync", ex.ToString());
                return ApiResponse<DashboardResponseDto>.Failed("An error occurred while retrieving dashboard data", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<DashboardStatsResponseDto>> GetDashboardStatsAsync()
        {
            try
            {
                WatchLogger.Log("Getting dashboard statistics", "GetDashboardStatsAsync");

                var stats = await GetDashboardStatsInternalAsync();

                return ApiResponse<DashboardStatsResponseDto>.Success(stats, "Dashboard statistics retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dashboard statistics");
                WatchLogger.LogError($"Error retrieving dashboard statistics: {ex.Message}", "GetDashboardStatsAsync", ex.ToString());
                return ApiResponse<DashboardStatsResponseDto>.Failed("An error occurred while retrieving dashboard statistics", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<List<DashboardVendorApplicationResponseDto>>> GetPendingVendorApplicationsAsync(int count = 5)
        {
            try
            {
                WatchLogger.Log($"Getting {count} pending vendor applications for dashboard", "GetPendingVendorApplicationsAsync");

                var applications = await GetPendingVendorApplicationsInternalAsync(count);

                return ApiResponse<List<DashboardVendorApplicationResponseDto>>.Success(applications, "Pending vendor applications retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pending vendor applications");
                WatchLogger.LogError($"Error retrieving pending vendor applications: {ex.Message}", "GetPendingVendorApplicationsAsync", ex.ToString());
                return ApiResponse<List<DashboardVendorApplicationResponseDto>>.Failed("An error occurred while retrieving pending vendor applications", HttpStatusCode.InternalServerError);
            }
        }

        /// <inheritdoc/>
        public async Task<ApiResponse<DashboardRevenueResponseDto>> GetPlatformRevenueDataAsync()
        {
            try
            {
                WatchLogger.Log("Getting platform revenue data for dashboard", "GetPlatformRevenueDataAsync");

                var revenue = await GetPlatformRevenueDataInternalAsync();

                return ApiResponse<DashboardRevenueResponseDto>.Success(revenue, "Platform revenue data retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving platform revenue data");
                WatchLogger.LogError($"Error retrieving platform revenue data: {ex.Message}", "GetPlatformRevenueDataAsync", ex.ToString());
                return ApiResponse<DashboardRevenueResponseDto>.Failed("An error occurred while retrieving platform revenue data", HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        #region Private Dashboard Helper Methods

        private async Task<DashboardStatsResponseDto> GetDashboardStatsInternalAsync()
        {
            var currentDate = DateTime.UtcNow;
            var lastMonthStart = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(-1);
            var thisMonthStart = new DateTime(currentDate.Year, currentDate.Month, 1);

            // Total users
            var totalUsers = await _dbContext.Users.CountAsync();
            var lastMonthUsers = await _dbContext.Users.CountAsync(u => u.CreatedOn < thisMonthStart);
            var usersChangePercentage = lastMonthUsers > 0 ? ((decimal)(totalUsers - lastMonthUsers) / lastMonthUsers) * 100 : 0;

            // Active vendors (approved vendor applications)
            var activeVendors = await _dbContext.Vendors
                .CountAsync(v => v.Status == Common.Enums.VendorEnums.VendorApplicationStatus.Approved && !v.IsDeleted);
            
            // Pending vendors
            var pendingVendors = await _dbContext.Vendors
                .CountAsync(v => v.Status == Common.Enums.VendorEnums.VendorApplicationStatus.Pending && !v.IsDeleted);

            // Total listings (this would need a Listings table - using placeholder for now)
            var totalListings = 3847; // Placeholder - implement when Listings entity is available
            var totalListingsChangePercentage = 12.5m; // Placeholder

            // Platform revenue (this would need a Transactions/Payments table - using placeholder for now)
            var platformRevenue = 45230m; // Placeholder - implement when Transactions entity is available
            var lastMonthRevenue = 38540m; // Placeholder
            var revenueChangePercentage = lastMonthRevenue > 0 ? ((platformRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

            return new DashboardStatsResponseDto
            {
                TotalUsers = totalUsers,
                TotalUsersChangePercentage = usersChangePercentage,
                ActiveVendors = activeVendors,
                PendingVendors = pendingVendors,
                TotalListings = totalListings,
                TotalListingsChangePercentage = totalListingsChangePercentage,
                PlatformRevenue = platformRevenue,
                PlatformRevenueChangePercentage = revenueChangePercentage,
                LastMonthRevenue = lastMonthRevenue
            };
        }

        private async Task<List<DashboardVendorApplicationResponseDto>> GetPendingVendorApplicationsInternalAsync(int count)
        {
            var applications = await _dbContext.Vendors
                .Where(v => v.Status == Common.Enums.VendorEnums.VendorApplicationStatus.Pending && !v.IsDeleted)
                .OrderBy(v => v.SubmissionDate)
                .Take(count)
                .Select(v => new DashboardVendorApplicationResponseDto
                {
                    Id = v.Id,
                    BusinessName = v.BusinessName,
                    LicenseNumber = v.TradeLicenseNumber,
                    BusinessEmail = v.BusinessEmail,
                    SubmissionDate = v.SubmissionDate,
                    Status = v.Status.ToString(),
                    DaysSinceSubmission = (int)(DateTime.UtcNow - v.SubmissionDate).TotalDays
                })
                .ToListAsync();

            return applications;
        }

        private Task<DashboardSupportTicketsResponseDto> GetSupportTicketsDataInternalAsync()
        {
            // This would need a SupportTickets table - using placeholder data for now
            // When implemented, query the actual support tickets table
            
            var result = new DashboardSupportTicketsResponseDto
            {
                OpenTickets = 12,
                InProgressTickets = 8,
                ResolvedToday = 15,
                RecentTickets = new List<DashboardSupportTicketResponseDto>()
                // Add actual ticket data when SupportTickets entity is available
            };

            return Task.FromResult(result);
        }

        private Task<DashboardRevenueResponseDto> GetPlatformRevenueDataInternalAsync()
        {
            // This would need a Transactions/Revenue table - using placeholder data for now
            var currentMonth = DateTime.UtcNow;
            var currentMonthRevenue = 45230m;
            var lastMonthRevenue = 38540m;
            var growthPercentage = ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100;

            // Generate monthly revenue data for the last 12 months
            var monthlyRevenue = new List<MonthlyRevenueDto>();
            for (int i = 11; i >= 0; i--)
            {
                var month = currentMonth.AddMonths(-i);
                monthlyRevenue.Add(new MonthlyRevenueDto
                {
                    Month = month.ToString("MMM"),
                    Year = month.Year,
                    Revenue = (decimal)(30000 + (i * 1500) + new Random().Next(-5000, 10000)) // Placeholder data
                });
            }

            var revenueBySource = new List<RevenueSourceDto>
            {
                new() { Source = "Subscription Fees", Amount = 25000m, Percentage = 55.3m },
                new() { Source = "Commission", Amount = 15000m, Percentage = 33.2m },
                new() { Source = "Advertising", Amount = 5230m, Percentage = 11.5m }
            };

            var result = new DashboardRevenueResponseDto
            {
                CurrentMonthRevenue = currentMonthRevenue,
                LastMonthRevenue = lastMonthRevenue,
                GrowthPercentage = growthPercentage,
                MonthlyRevenue = monthlyRevenue,
                RevenueBySource = revenueBySource
            };

            return Task.FromResult(result);
        }

        #endregion
    }
}
