﻿using Microsoft.OpenApi.Models;
using System.Reflection;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Swagger service estention class
    /// </summary>
    public static class SwaggerServiceExtension
    {
        /// <summary>
        /// Method to add swagger configuration
        /// </summary>
        /// <param name="services"></param>
        public static void AddSwaggerConfig(this IServiceCollection services) =>
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "DUBAI CAR MARKETPLACE API",
                    Version = "V1",
                    Description = "DUBAI CAR MARKETPLACE API: This api is used to power a web and mobile application for car market place.",
                    Contact = new OpenApiContact
                    {
                        Name = "DUBAI CAR MARKETPLACE API",
                        Email = "<EMAIL>",
                        Url = new Uri("https://www.redmi.com/")
                    }
                });
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                c.IncludeXmlComments(xmlPath);

                c.AddSecurityDefinition("Bearer",
                    new OpenApiSecurityScheme
                    {
                        In = ParameterLocation.Header,
                        Description = "JWT Authorization header using token",
                        BearerFormat = "JWT",
                        Name = "Authorization",
                        Type = SecuritySchemeType.Http,
                        Scheme = "Bearer"
                    });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            },
                            Name = "Bearer",
                        },
                        new List<string>()
                    }
                });
            });
    }
}
