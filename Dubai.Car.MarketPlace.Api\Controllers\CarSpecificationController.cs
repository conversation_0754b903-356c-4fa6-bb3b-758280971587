using Dubai.Car.MarketPlace.Api.Attributes;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// Controller for car specification management
    /// </summary>
    [ApiController]
    [Route("api/car-specifications")]
    public class CarSpecificationController : BaseController
    {
        private readonly ICarSpecificationService _carSpecificationService;

        /// <summary>
        /// Initializes a new instance of the <see cref="CarSpecificationController"/> class.
        /// </summary>
        /// <param name="carSpecificationService">The service used to manage car specifications. This parameter cannot be null.</param>
        public CarSpecificationController(ICarSpecificationService carSpecificationService)
        {
            _carSpecificationService = carSpecificationService;
        }

        /// <summary>
        /// Gets all car specifications
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive specifications</param>
        /// <returns>List of specifications</returns>
        [HttpGet("get-specs")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<CarSpecificationResponseDto>>>> GetSpecifications([FromQuery] bool includeInactive = false)
        {
            var result = await _carSpecificationService.GetSpecificationsAsync(includeInactive);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets car specifications grouped by category
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive specifications</param>
        /// <returns>Grouped specifications</returns>
        [HttpGet("get-specs-grouped")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<List<GroupedCarSpecificationResponseDto>>>> GetSpecificationsGrouped([FromQuery] bool includeInactive = false)
        {
            var result = await _carSpecificationService.GetSpecificationsGroupedByCategoryAsync(includeInactive);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Gets a car specification by ID
        /// </summary>
        /// <param name="id">Specification ID</param>
        /// <returns>Specification details</returns>
        [HttpGet("get-spec/{id}")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarSpecificationResponseDto>>> GetSpecification(Guid id)
        {
            var result = await _carSpecificationService.GetSpecificationByIdAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Creates a new car specification (Admin only)
        /// </summary>
        /// <param name="request">Specification creation request</param>
        /// <returns>Created specification</returns>
        [HttpPost("create-spec")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarSpecificationResponseDto>>> CreateSpecification([FromBody] CreateCarSpecificationRequest request)
        {
            var result = await _carSpecificationService.CreateSpecificationAsync(request);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates an existing car specification (Admin only)
        /// </summary>
        /// <param name="id">Specification ID</param>
        /// <param name="request">Specification update request</param>
        /// <returns>Updated specification</returns>
        [HttpPut("update-spec/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<CarSpecificationResponseDto>>> UpdateSpecification(Guid id, [FromBody] UpdateCarSpecificationRequest request)
        {
            var result = await _carSpecificationService.UpdateSpecificationAsync(id, request);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Deletes a car specification (Admin only)
        /// </summary>
        /// <param name="id">Specification ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteSpecification(Guid id)
        {
            var result = await _carSpecificationService.DeleteSpecificationAsync(id);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Toggles specification active status (Admin only)
        /// </summary>
        /// <param name="id">Specification ID</param>
        /// <param name="request">Toggle status request</param>
        /// <returns>Success response</returns>
        [HttpPatch("toggle-status/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> ToggleSpecificationStatus(Guid id, [FromBody] ToggleSpecificationStatusRequest request)
        {
            var result = await _carSpecificationService.ToggleSpecificationStatusAsync(id, request.IsActive);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Updates display order of specifications (Admin only)
        /// </summary>
        /// <param name="request">Display order update request</param>
        /// <returns>Success response</returns>
        [HttpPatch("update-display-order")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<bool>>> UpdateDisplayOrder([FromBody] UpdateDisplayOrderRequest request)
        {
            var result = await _carSpecificationService.UpdateSpecificationDisplayOrderAsync(request.SpecificationOrders);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Validates a specification value
        /// </summary>
        /// <param name="id">Specification ID</param>
        /// <param name="request">Validation request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-spec")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<ActionResult<ApiResponse<SpecificationValidationResult>>> ValidateSpecificationValue(Guid id, [FromBody] ValidateSpecificationValueRequest request)
        {
            var result = await _carSpecificationService.ValidateSpecificationValueAsync(id, request.Value);
            return StatusCode((int)result.StatusCode, result);
        }
    }
}
