@Dubai.Car.MarketPlace.Api_HostAddress = http://localhost:5094
@contentType = application/json
@token = your-super-admin-jwt-token-here

### 1. Create Admin User
POST {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/create-admin
Content-Type: {{contentType}}
Authorization: Bearer {{token}}

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+************",
  "roleId": "role-guid-here",
  "temporaryPassword": "TempPass123!",
  "gender": "Male",
  "country": "UAE",
  "state": "Dubai"
}

### 2. Get All Admin Users
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/get-all-admins?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### 3. Get Admin User by ID
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/get-admin/user-id-here
Authorization: Bearer {{token}}

### 4. Update Admin User
PUT {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/update-admin/user-id-here
Content-Type: {{contentType}}
Authorization: Bearer {{token}}

{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phoneNumber": "+************",
  "roleId": "role-guid-here",
  "gender": "Female",
  "country": "UAE",
  "state": "Abu Dhabi"
}

### 5. Lock Admin User
PUT {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/lock-unlock-admin/user-id-here
Content-Type: {{contentType}}
Authorization: Bearer {{token}}

{
  "isLocked": true,
  "lockReason": "Security violation"
}

### 6. Unlock Admin User
PUT {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/lock-unlock-admin/user-id-here
Content-Type: {{contentType}}
Authorization: Bearer {{token}}

{
  "isLocked": false,
  "lockReason": "Account restored"
}

### 7. Get All Permissions
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/get-permissions
Authorization: Bearer {{token}}

### 8. Assign Role Permissions
POST {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/assign-role-permissions
Content-Type: {{contentType}}
Authorization: Bearer {{token}}

{
  "roleId": "role-guid-here",
  "permissionIds": [
    "permission-guid-1",
    "permission-guid-2",
    "permission-guid-3"
  ]
}

### 9. Get All Roles with Permissions
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/get-roles-with-permissions
Authorization: Bearer {{token}}

### 10. Get Role with Permissions by ID
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/get-role-with-permissions/role-id-here
Authorization: Bearer {{token}}

### 11. Delete Admin User
DELETE {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/admin/delete-admin/user-id-here
Authorization: Bearer {{token}}

### Authentication Endpoints (for getting tokens)
# Note: These should be in your existing auth endpoints

### Login to get token
POST {{Dubai.Car.MarketPlace.Api_HostAddress}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "your-password-here"
}

### Health Check
GET {{Dubai.Car.MarketPlace.Api_HostAddress}}/health
