using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    public static class PermissionSeeder
    {
        public static async Task SeedPermissionsAsync(CarContext context, ILogger logger)
        {
            try
            {
                // Check if permissions already exist
                if (await context.Permissions.AnyAsync())
                {
                    logger.LogInformation("Permissions already exist. Skipping permission seeding.");
                    return;
                }

                var permissions = new List<Permission>();

                // Get all enum values and create permissions dynamically
                var permissionValues = Enum.GetValues<RoleEnums.SystemPermissions>();
                
                foreach (var permission in permissionValues)
                {
                    permissions.Add(new Permission
                    {
                        Id = Guid.NewGuid(),
                        Name = permission.ToString(), // Use enum name for parsing
                        Description = permission.GetDescription(), // Keep description for display
                        CreatedOn = DateTime.UtcNow,
                        IsDeleted = false
                    });
                }

                context.Permissions.AddRange(permissions);
                await context.SaveChangesAsync();

                logger.LogInformation($"Successfully seeded {permissions.Count} permissions.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding permissions.");
                throw;
            }
        }
    }
}
