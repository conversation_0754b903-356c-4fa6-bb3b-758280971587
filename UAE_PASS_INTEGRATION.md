# UAE PASS Integration Documentation

## Overview

This document describes the UAE PASS integration implemented in the Dubai Car MarketPlace API. UAE PASS acts as an external identity provider that integrates seamlessly with the existing JWT-based authentication system.

## Features

- **OAuth 2.0 Integration**: Full OAuth 2.0 authorization code flow implementation
- **User Matching**: Automatic user matching based on Emirates ID, UAE PASS User ID, or email
- **User Creation**: Automatic user creation for new UAE PASS users
- **JWT Token Generation**: Issues standard JWT tokens for UAE PASS authenticated users
- **CSRF Protection**: State parameter validation for security
- **Error Handling**: Comprehensive error handling and logging

## Configuration

### appsettings.json

Add the following configuration to your `appsettings.json` and `appsettings.Development.json`:

```json
{
  "UaePassSettings": {
    "ClientId": "your-uae-pass-client-id",
    "ClientSecret": "your-uae-pass-client-secret",
    "AuthorizationEndpoint": "https://stg-id.uaepass.ae/idshub/authorize",
    "TokenEndpoint": "https://stg-id.uaepass.ae/idshub/token",
    "UserInfoEndpoint": "https://stg-id.uaepass.ae/idshub/userinfo",
    "RedirectUri": "http://localhost:5094/api/auth/uaepass/callback",
    "Scope": "urn:uae:digitalid:profile:general",
    "IsSandbox": true,
    "TimeoutSeconds": 30
  }
}
```

### Environment-Specific Settings

- **Development**: Uses `http://localhost:5094/api/auth/uaepass/callback`
- **Production**: Uses `https://schoolpaymetsystem.onrender.com/api/auth/uaepass/callback`
- **Sandbox**: Set `IsSandbox: true` for testing with UAE PASS staging environment

## API Endpoints

### 1. Get Authorization URL

**Endpoint**: `GET /api/auth/uaepass/start`

**Description**: Generates the UAE PASS authorization URL for frontend redirection.

**Response**:
```json
{
  "isSuccess": true,
  "statusCode": 200,
  "message": "Authorization URL generated successfully",
  "data": {
    "authorizationUrl": "https://stg-id.uaepass.ae/idshub/authorize?response_type=code&client_id=...",
    "state": "generated-csrf-state"
  }
}
```

### 2. Handle OAuth Callback (POST)

**Endpoint**: `POST /api/auth/uaepass/callback`

**Description**: Handles the OAuth callback from UAE PASS and authenticates the user.

**Request Body**:
```json
{
  "code": "authorization-code-from-uaepass",
  "state": "csrf-state-parameter",
  "error": null,
  "errorDescription": null
}
```

**Response**:
```json
{
  "isSuccess": true,
  "statusCode": 200,
  "message": "UAE PASS authentication successful",
  "data": {
    "userId": "user-guid",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "emailConfirmed": true,
    "token": "jwt-token",
    "refreshToken": "refresh-token",
    "refreshTokenExpiryTime": "2024-01-01T00:00:00Z",
    "role": "Customer",
    "userType": "Customer",
    "permissions": []
  }
}
```

### 3. Handle OAuth Callback (GET)

**Endpoint**: `GET /api/auth/uaepass/callback`

**Description**: Alternative callback endpoint for direct browser redirects.

**Query Parameters**:
- `code`: Authorization code from UAE PASS
- `state`: CSRF protection state parameter
- `error`: Error code (if any)
- `error_description`: Error description (if any)

## Integration Flow

### Frontend Integration

1. **Initiate Login**: Call `GET /api/auth/uaepass/start` to get authorization URL
2. **Redirect User**: Redirect user to the returned authorization URL
3. **Handle Callback**: UAE PASS redirects back to your callback URL with authorization code
4. **Exchange Code**: Call `POST /api/auth/uaepass/callback` with the authorization code
5. **Store Token**: Store the returned JWT token for subsequent API calls

### Example Frontend Code (JavaScript)

```javascript
// Step 1: Get authorization URL
async function initiateUaePassLogin() {
  const response = await fetch('/api/auth/uaepass/start');
  const data = await response.json();
  
  if (data.isSuccess) {
    // Redirect user to UAE PASS
    window.location.href = data.data.authorizationUrl;
  }
}

// Step 2: Handle callback (in your callback page)
async function handleUaePassCallback() {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const state = urlParams.get('state');
  const error = urlParams.get('error');
  
  if (error) {
    console.error('UAE PASS authentication failed:', error);
    return;
  }
  
  const response = await fetch('/api/auth/uaepass/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ code, state })
  });
  
  const data = await response.json();
  
  if (data.isSuccess) {
    // Store JWT token
    localStorage.setItem('authToken', data.data.token);
    localStorage.setItem('refreshToken', data.data.refreshToken);
    
    // Redirect to dashboard or home page
    window.location.href = '/dashboard';
  }
}
```

## User Matching Logic

The system attempts to match UAE PASS users with existing users in the following order:

1. **Emirates ID**: If available, matches by `EmiratesId` field
2. **UAE PASS User ID**: Matches by `UaePassUserId` field
3. **Email Address**: Matches by email address

If no existing user is found, a new user account is automatically created with:
- Customer role assignment
- Email verification set to true (UAE PASS users are pre-verified)
- UAE PASS specific fields populated

## Database Schema Changes

The following fields were added to the `User` entity:

```csharp
public string? EmiratesId { get; set; }
public string? UaePassUserId { get; set; }
public string? UaePassUuid { get; set; }
public bool IsUaePassVerified { get; set; } = false;
public DateTime? UaePassLastLogin { get; set; }
```

## Security Considerations

- **CSRF Protection**: State parameter validation prevents CSRF attacks
- **Token Validation**: All UAE PASS tokens are validated before use
- **Secure Storage**: Sensitive configuration stored in secure configuration
- **Error Handling**: No sensitive information exposed in error messages
- **Logging**: Comprehensive logging for security monitoring

## Testing

Run the unit tests to verify the integration:

```bash
dotnet test Dubai.Car.MarketPlace.Test
```

## Troubleshooting

### Common Issues

1. **Invalid Client ID/Secret**: Verify UAE PASS credentials in configuration
2. **Redirect URI Mismatch**: Ensure redirect URI matches UAE PASS app configuration
3. **Network Timeouts**: Check network connectivity and timeout settings
4. **State Validation Errors**: Ensure state parameter is properly maintained

### Logging

All UAE PASS operations are logged using WatchDog. Check the logs for detailed error information.

## Production Deployment

Before deploying to production:

1. Update `UaePassSettings` with production UAE PASS endpoints
2. Set `IsSandbox: false` in production configuration
3. Ensure SSL/TLS is properly configured
4. Update redirect URIs in UAE PASS application settings
5. Test the complete flow in production environment

## Support

For UAE PASS specific issues, refer to the official UAE PASS documentation or contact UAE PASS support.
