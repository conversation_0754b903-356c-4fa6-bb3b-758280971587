﻿using WatchDog;
using WatchDog.src.Enums;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// WatchDog service extension methods
    /// </summary>
    public static class WatchDogServiceExtension
    {
        /// <summary>
        /// Watchdog configuration extension method
        /// </summary>
        /// <param name="services"></param>
        /// <param name="conn"></param>
        public static void AddWatchDogConfig(this IServiceCollection services, string conn)
        {
            services.AddWatchDogServices(opt =>
            {
                opt.IsAutoClear = true;
                opt.ClearTimeSchedule = WatchDogAutoClearScheduleEnum.Weekly;
                opt.SetExternalDbConnString = conn;
                opt.DbDriverOption = WatchDogDbDriverEnum.PostgreSql;
            });
        }
    }
}
