### Get Current User Permissions
GET {{baseUrl}}/api/user/get-permissions
Authorization: Bearer {{authToken}}
Content-Type: application/json

###

# Example Response:
# {
#   "isSuccess": true,
#   "message": "Success",
#   "statusCode": 200,
#   "data": {
#     "userId": "12345678-1234-1234-1234-123456789012",
#     "email": "<EMAIL>",
#     "roles": ["Admin"],
#     "permissions": [
#       "UserManagement",
#       "VendorManagement",
#       "ContentManagement",
#       "SystemSettings"
#     ],
#     "permissionDetails": [
#       {
#         "name": "UserManagement",
#         "description": "User Management",
#         "permission": "UserManagement"
#       },
#       {
#         "name": "VendorManagement", 
#         "description": "Vendor Management",
#         "permission": "VendorManagement"
#       }
#     ]
#   }
# }
