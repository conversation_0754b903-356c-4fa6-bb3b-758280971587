﻿using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    public interface IAwsService
    {
        Task<string> UploadFileAsync(IFormFile file, string folderName);
        Task<string> UploadLargeFileAsync(IFormFile file, string folderName, IProgress<int>? progress = null);
        Task<bool> DeleteFileAsync(string key);
        Task<string> GetPresignedUrlAsync(string key);
        string GenerateS3Key(string folderName, string fileName);
        Task<int> CleanupTempFilesAsync(int olderThanHours);
    }
}
