using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Data.Entities;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    /// <summary>
    /// Test data helper for demonstrating RegisterAsync functionality
    /// </summary>
    public static class TestDataHelper
    {
        /// <summary>
        /// Creates sample RegisterRequestDto objects for testing
        /// </summary>
        /// <returns>List of sample registration requests</returns>
        public static List<RegisterRequestDto> GetSampleRegistrationRequests()
        {
            return new List<RegisterRequestDto>
            {
                new RegisterRequestDto
                {
                    FirstName = "Emily",
                    LastName = "Brown",
                    Email = "<EMAIL>",
                    Password = "Password123!",
                    ConfirmPassword = "Password123!"
                },
                new RegisterRequestDto
                {
                    FirstName = "Mohammed",
                    LastName = "Hassan",
                    Email = "<EMAIL>",
                    Password = "Password123!",
                    ConfirmPassword = "Password123!"
                },
                new RegisterRequestDto
                {
                    FirstName = "Lisa",
                    LastName = "Anderson",
                    Email = "<EMAIL>",
                    Password = "Password123!",
                    ConfirmPassword = "Password123!"
                },
                new RegisterRequestDto
                {
                    FirstName = "Omar",
                    LastName = "Al-Zahra",
                    Email = "<EMAIL>",
                    Password = "Password123!",
                    ConfirmPassword = "Password123!"
                },
                new RegisterRequestDto
                {
                    FirstName = "Anna",
                    LastName = "Kowalski",
                    Email = "<EMAIL>",
                    Password = "Password123!",
                    ConfirmPassword = "Password123!"
                }
            };
        }
    }
}
