using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Logic.Constants;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using WatchDog;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl;

/// <summary>
/// Service for warming up the cache with frequently accessed data
/// </summary>
public class CacheWarmingService : IHostedService, IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CacheWarmingService> _logger;
    private readonly CacheConfiguration _cacheConfig;
    private Timer? _timer;

    /// <summary>
    /// Initializes a new instance of the <see cref="CacheWarmingService"/> class, which is responsible for preloading
    /// cache data to improve application performance.
    /// </summary>
    /// <param name="serviceProvider">The service provider used to resolve dependencies required for cache warming operations.</param>
    /// <param name="logger">The logger used to log diagnostic and operational information during cache warming.</param>
    /// <param name="cacheConfig">The configuration settings for cache warming, including cache size and expiration policies.</param>
    public CacheWarmingService(
        IServiceProvider serviceProvider,
        ILogger<CacheWarmingService> logger,
        CacheConfiguration cacheConfig)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _cacheConfig = cacheConfig;
    }

    /// <summary>
    /// Starts the cache warming service.
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        if (!_cacheConfig.EnableCacheWarming)
        {
            _logger.LogInformation("Cache warming is disabled");
            WatchLogger.Log("Cache warming is disabled");
            return Task.CompletedTask;
        }

        _logger.LogInformation("Cache Warming Service is starting");
        WatchLogger.Log("Cache Warming Service is starting");

        _timer = new Timer(DoWork, null, TimeSpan.Zero, 
            TimeSpan.FromMinutes(_cacheConfig.CacheWarmingIntervalMinutes));

        return Task.CompletedTask;
    }

    /// <summary>
    /// Performs the cache warming operation by preloading recent payment and student data into the cache.
    /// This method is intended to be run on a schedule to ensure that frequently accessed data
    /// is available in the cache for improved performance.
    /// </summary>
    /// <param name="state">An optional state object passed by the timer or scheduler.</param>
    /// <remarks>
    /// Retrieves recent payments and students from the database, checks if they are already cached, and if not,
    /// adds them to the cache with the configured expiration. Logs the outcome of the operation.
    /// </remarks>
    private async void DoWork(object? state)
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<CarContext>();
        var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();

        // Get recent payments
        //var recentDate = DateTime.UtcNow.AddDays(-_cacheConfig.PaymentDataPrewarmDays);
        //var recentPayments = await dbContext.FeePayments
        //    .Include(p => p.School)
        //    .Include(p => p.FeePaymentItems)
        //        .ThenInclude(pi => pi.StudentFee)
        //            .ThenInclude(sf => sf.Fee)
        //    .Include(p => p.FeePaymentItems)
        //        .ThenInclude(pi => pi.StudentFee)
        //            .ThenInclude(sf => sf.Student)
        //    .Where(p => p.PaymentDate >= recentDate && !p.IsDeleted)
        //    .ToListAsync();

        //foreach (var payment in recentPayments)
        //{
        //    var cacheKey = $"fee_payment_{payment.Id}";
        //    if (!await cacheService.ExistsAsync(cacheKey))
        //    {
        //        await cacheService.SetAsync(cacheKey, payment, _cacheConfig.DefaultExpirationMinutes);
        //        _logger.LogInformation("Warmed cache for payment {PaymentId}", payment.Id);
        //    }
        //}

        //// Preload recent student data into the cache
        //var recentStudentDate = DateTime.UtcNow.AddDays(-_cacheConfig.PaymentDataPrewarmDays);
        //var recentStudents = await dbContext.Students
        //    .Include(s => s.School)
        //    .Include(s => s.Class)
        //    .Where(s => s.CreatedOn >= recentStudentDate && !s.IsDeleted)
        //    .ToListAsync();

        //foreach (var student in recentStudents)
        //{
        //    var studentCacheKey = CacheKeys.Student(student.Id);
        //    if (!await cacheService.ExistsAsync(studentCacheKey))
        //    {
        //        await cacheService.SetAsync(studentCacheKey, student, _cacheConfig.DefaultExpirationMinutes);
        //        _logger.LogInformation("Warmed cache for student {StudentId}", student.Id);
        //    }
        //}

        _logger.LogInformation("Cache warming completed. Processed {PaymentCount} payments and {StudentCount} students", "hh");
        WatchLogger.Log("Cache warming completed. Processed {PaymentCount} payments and {StudentCount} students\", recentPayments.Count, recentStudents.Count");
    }

    /// <summary>
    /// Stops the cache warming service and releases associated resources.
    /// </summary>
    /// <param name="cancellationToken">A token that can be used to signal the operation should be canceled. This parameter is not used in the current
    /// implementation.</param>
    /// <returns>A completed <see cref="Task"/> representing the asynchronous stop operation.</returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cache Warming Service is stopping");
        WatchLogger.Log("Cache Warming Service is stopping");

        _timer?.Change(Timeout.Infinite, 0);

        return Task.CompletedTask;
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
} 