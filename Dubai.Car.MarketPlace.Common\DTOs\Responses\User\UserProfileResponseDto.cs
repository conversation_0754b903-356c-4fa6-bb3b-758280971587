﻿using static Dubai.Car.MarketPlace.Common.Enums.UserEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.User
{
    /// <summary>
    /// DTO for user profile information response
    /// </summary>
    public class UserProfileResponseDto
    {
        /// <summary>
        /// User ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;
        
        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;
        
        /// <summary>
        /// User's full name (FirstName + LastName)
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";
        
        /// <summary>
        /// User's gender
        /// </summary>
        public string Gender { get; set; } = string.Empty;
        
        /// <summary>
        /// User's designation or job title
        /// </summary>
        public string? Designation { get; set; }
        
        /// <summary>
        /// User's phone number
        /// </summary>
        public string? PhoneNumber { get; set; }
        
        /// <summary>
        /// Whether the user's email is confirmed
        /// </summary>
        public bool EmailConfirmed { get; set; }
        
        /// <summary>
        /// User's role
        /// </summary>
        public string Role { get; set; } = string.Empty;
        
        /// <summary>
        /// S3 key for the user's profile picture
        /// </summary>
        public string? ProfilePictureKey { get; set; }
        
        /// <summary>
        /// URL to access the user's profile picture
        /// </summary>
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// Gets or sets the current status of the user.
        /// </summary>
        public UserStatus Status { get; set; }

        /// <summary>
        /// Date when the user was created
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// Date when the user was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }
}
