using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for available permissions
    /// </summary>
    public class PermissionResponseDto
    {
        /// <summary>
        /// Permission ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Permission name
        /// </summary>
        public string Name { get; set; } = default!;

        /// <summary>
        /// Permission description
        /// </summary>
        public string Description { get; set; } = default!;

        /// <summary>
        /// Permission value from enum
        /// </summary>
        public SystemPermissions Permission { get; set; }
    }
}
