using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.User
{
    /// <summary>
    /// Response DTO for user permissions
    /// </summary>
    public class UserPermissionsResponseDto
    {
        /// <summary>
        /// User ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// User email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's roles
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();

        /// <summary>
        /// User's permissions
        /// </summary>
        public List<SystemPermissions> Permissions { get; set; } = new List<SystemPermissions>();

        /// <summary>
        /// Permission names with descriptions
        /// </summary>
        public List<PermissionDetail> PermissionDetails { get; set; } = new List<PermissionDetail>();
    }

    /// <summary>
    /// Permission detail with name and description
    /// </summary>
    public class PermissionDetail
    {
        /// <summary>
        /// Permission name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Permission description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Permission enum value
        /// </summary>
        public SystemPermissions Permission { get; set; }
    }
}
