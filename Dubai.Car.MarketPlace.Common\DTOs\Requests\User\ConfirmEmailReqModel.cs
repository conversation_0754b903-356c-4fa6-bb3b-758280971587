using System;
using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.User
{
    /// <summary>
    /// Model for confirming a user's email
    /// </summary>
    public class ConfirmEmailReqModel
    {
        /// <summary>
        /// The ID of the user whose email is being confirmed
        /// </summary>
        [Required]
        public string UserId { get; set; } = default!;

        /// <summary>
        /// The token generated for email confirmation
        /// </summary>
        [Required]
        public string Token { get; set; } = default!;

        /// <summary>
        /// The email being confirmed
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; } = default!;
    }
}
