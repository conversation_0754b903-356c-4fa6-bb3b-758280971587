using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a car body type in the system
    /// </summary>
    public class BodyType : BaseEntity
    {
        /// <summary>
        /// The name of the body type (e.g., Sedan, SUV, Hatchback, Coupe)
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = default!;

        /// <summary>
        /// The description of the body type
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// The icon image key for storage (S3, etc.)
        /// </summary>
        public string? IconImageKey { get; set; }

        /// <summary>
        /// Whether this body type is currently active for new car listings
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting body types in UI
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        // Navigation properties
        /// <summary>
        /// Cars associated with this body type
        /// </summary>
        public virtual ICollection<Car> Cars { get; set; } = new List<Car>();
    }
}
