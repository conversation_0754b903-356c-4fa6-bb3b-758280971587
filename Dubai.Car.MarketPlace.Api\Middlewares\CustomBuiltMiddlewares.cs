﻿namespace Dubai.Car.MarketPlace.Api.Middlewares
{
    /// <summary>
    /// Provides a collection of custom middleware extensions for configuring the application's request pipeline.
    /// </summary>
    /// <remarks>This class contains static methods for adding custom middleware to an ASP.NET Core
    /// application's request pipeline. The middleware can be used to handle specific concerns such as exception
    /// handling or token validation.</remarks>
    public static class CustomBuiltMiddlewares
    {
        /// <summary>
        /// Adds custom exception handling middleware to the application's request pipeline.
        /// </summary>
        /// <remarks>This method configures the application to use middleware for handling exceptions. The
        /// middleware intercepts unhandled exceptions and provides a centralized mechanism for logging and returning
        /// appropriate error responses.</remarks>
        /// <param name="app">The <see cref="IApplicationBuilder"/> instance used to configure the application's request pipeline.</param>
        public static void UseCustomExceptionMiddlewares(this IApplicationBuilder app)
        {
            app.UseMiddleware<ExceptionMiddleware>();
            //app.UseMiddleware<TokenValidationMiddleware>();
        }
    }
}
