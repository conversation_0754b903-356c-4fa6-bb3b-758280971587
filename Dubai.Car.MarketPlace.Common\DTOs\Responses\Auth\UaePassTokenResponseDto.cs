using System.Text.Json.Serialization;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth
{
    /// <summary>
    /// Response DTO for UAE PASS token exchange
    /// </summary>
    public class UaePassTokenResponseDto
    {
        /// <summary>
        /// Access token from UAE PASS
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Token type (usually "Bearer")
        /// </summary>
        [JsonPropertyName("token_type")]
        public string TokenType { get; set; } = string.Empty;

        /// <summary>
        /// Token expiration time in seconds
        /// </summary>
        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        /// <summary>
        /// Refresh token for getting new access tokens
        /// </summary>
        [JsonPropertyName("refresh_token")]
        public string? RefreshToken { get; set; }

        /// <summary>
        /// Scope of the access token
        /// </summary>
        [Json<PERSON>ropertyName("scope")]
        public string? Scope { get; set; }

        /// <summary>
        /// ID token containing user claims
        /// </summary>
        [JsonPropertyName("id_token")]
        public string? IdToken { get; set; }
    }
}
