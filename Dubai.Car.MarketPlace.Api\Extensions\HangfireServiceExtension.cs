using Dubai.Car.MarketPlace.Logic.Services.Impl;
using Hangfire;
using Hangfire.Dashboard;
using Hangfire.PostgreSql;
using WatchDog;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Extension methods for configuring Hangfire services
    /// </summary>
    public static class HangfireServiceExtension
    {
        /// <summary>
        /// Adds Hangfire services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <param name="connectionString"></param>
        public static void AddHangfireServices(this IServiceCollection services, IConfiguration configuration, string connectionString)
        {
            // Add Hangfire services
            services.AddHangfire(config => config
                .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UsePostgreSqlStorage(options =>
                {
                    options.UseNpgsqlConnection(connectionString);
                }));

            // Get worker count from configuration
            var workerCount = configuration.GetValue<int>("Hangfire:WorkerCount", 20);

            // Add the Hangfire server with configuration
            services.AddHangfireServer(options =>
            {
                options.WorkerCount = workerCount;
                options.Queues = new[] { "default" };
                options.ServerName = $"{Environment.MachineName}:{Environment.ProcessId}";
            });

            // Register the health check monitoring service
            services.AddHttpClient();
            services.AddScoped<HealthCheckMonitoringService>();
        }

        /// <summary>
        /// Configures Hangfire and schedules background jobs
        /// </summary>
        /// <param name="app">The application builder</param>
        public static void UseHangfireConfiguration(this IApplicationBuilder app)
        {
            // Get configuration
            var serviceProvider = app.ApplicationServices;
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();

            // Get dashboard path from configuration
            var dashboardPath = configuration.GetValue<string>("Hangfire:DashboardPath", "/hangfire");

            // Use Hangfire dashboard with authorization filter
            app.UseHangfireDashboard(dashboardPath, new DashboardOptions
            {
                Authorization = new[] { new HangfireAuthorizationFilter(configuration) },
                DashboardTitle = "School Payment System - Background Jobs",
                DisplayStorageConnectionString = false, // Hide connection string for security
                IsReadOnlyFunc = (context) =>
                {
                    // Make dashboard read-only in production for safety
                    var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                    return env != "Development" && env != "QA";
                }
            });

            // Check if health check monitoring is enabled
            var enableHealthCheckMonitoring = configuration.GetValue<bool>("AppConfig:EnableHealthCheckMonitoring");
            if (enableHealthCheckMonitoring)
            {
                // Schedule recurring jobs
                RecurringJob.AddOrUpdate<HealthCheckMonitoringService>(
                    "health-check-monitoring",
                    service => service.PerformHealthCheck(),
                     "*/5 * * * *");

                // Run a health check immediately on startup
                var backgroundJobClient = serviceProvider.GetRequiredService<IBackgroundJobClient>();
                backgroundJobClient.Enqueue<HealthCheckMonitoringService>(service => service.PerformHealthCheck());
                WatchLogger.Log("Health check monitoring job has been scheduled.");
                Console.WriteLine("Health check monitoring is enabled. Scheduled to run hourly.");
            }
            else
            {
                WatchLogger.Log("Health check monitoring is disabled.");
                Console.WriteLine("Health check monitoring is disabled.");
            }
        }
    }

    /// <summary>
    /// Authorization filter for Hangfire dashboard
    /// </summary>
    public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
    {
        private readonly IConfiguration? _configuration;
        private readonly string _allowedEnvironment;
        private readonly bool _requireAuthentication;

        /// <summary>
        /// Constructor for HangfireAuthorizationFilter
        /// </summary>
        /// <param name="configuration">The configuration</param>
        public HangfireAuthorizationFilter(IConfiguration configuration)
        {
            _configuration = configuration;
            _allowedEnvironment = "Development"; // Default to Development
            _requireAuthentication = false; // Default to not requiring authentication

            // Try to get configuration values if available
            var hangfireConfig = _configuration.GetSection("Hangfire");
            if (hangfireConfig != null)
            {
                _allowedEnvironment = hangfireConfig["AllowedEnvironment"] ?? _allowedEnvironment;
                _requireAuthentication = bool.TryParse(hangfireConfig["RequireAuthentication"], out bool requireAuth)
                    ? requireAuth
                    : _requireAuthentication;
            }
        }

        /// <summary>
        /// Default constructor for when configuration is not available
        /// </summary>
        public HangfireAuthorizationFilter()
        {
            // No configuration available in this constructor
            _configuration = null;
            _allowedEnvironment = "Development";
            _requireAuthentication = false;
        }

        /// <summary>
        /// Authorizes access to the Hangfire dashboard
        /// </summary>
        /// <param name="context">The dashboard context</param>
        /// <returns>True if access is authorized, false otherwise</returns>
        public bool Authorize(DashboardContext context)
        {
            // Get the current environment
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return true; // Allow access by default

            if (env == _allowedEnvironment)
            {
                return true;
            }

            // In other environments, check if authentication is required
            if (_requireAuthentication)
            {
                var httpContext = context.GetHttpContext();
                var res = httpContext.User.Identity?.IsAuthenticated == true && HasAdminRights(httpContext);
                if (res)
                {
                    // Log the reason for denial
                    httpContext.Response.StatusCode = 403; // Forbidden
                    httpContext.Response.WriteAsync("Access denied: You do not have sufficient rights.");
                    return false;
                }
            }

            // If we're not in the allowed environment and authentication is not required, deny access
            context.Response.StatusCode = 403;
            context.Response.WriteAsync("Access denied: Unauthorized environment.");
            return false;
        }

        /// <summary>
        /// Checks if the user has admin rights
        /// </summary>
        /// <param name="httpContext">The HTTP context</param>
        /// <returns>True if the user has admin rights, false otherwise</returns>
        private bool HasAdminRights(HttpContext httpContext)
        {
            if (httpContext.User.IsInRole("Admin"))
            {
                return true;
            }

            // Check if the user has the System Administrator role
            if (httpContext.User.IsInRole("System Administrator"))
            {
                return true;
            }

            // Check if the user has the required claims
            if (httpContext.User.HasClaim(c => c.Type == "Permission" && c.Value == "ManageBackgroundJobs"))
            {
                return true;
            }

            return false;
        }
    }
}
