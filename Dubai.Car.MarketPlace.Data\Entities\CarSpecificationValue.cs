using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    /// <summary>
    /// Represents a specification value for a specific car
    /// </summary>
    public class CarSpecificationValue : BaseEntity
    {
        /// <summary>
        /// The value of the specification
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string Value { get; set; } = default!;

        /// <summary>
        /// Additional notes or comments about this specification value
        /// </summary>
        [MaxLength(1000)]
        public string? Notes { get; set; }

        // Foreign Keys
        /// <summary>
        /// The ID of the car this specification belongs to
        /// </summary>
        public Guid CarId { get; set; }

        /// <summary>
        /// The ID of the specification template
        /// </summary>
        public Guid CarSpecificationId { get; set; }

        // Navigation properties
        /// <summary>
        /// The car this specification value belongs to
        /// </summary>
        public virtual Car Car { get; set; } = default!;

        /// <summary>
        /// The specification template
        /// </summary>
        public virtual CarSpecification CarSpecification { get; set; } = default!;
    }
}
