namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for support tickets in dashboard
    /// </summary>
    public class DashboardSupportTicketResponseDto
    {
        /// <summary>
        /// Ticket ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// Ticket subject/title
        /// </summary>
        public string Subject { get; set; } = default!;

        /// <summary>
        /// User who created the ticket
        /// </summary>
        public string UserName { get; set; } = default!;

        /// <summary>
        /// User email
        /// </summary>
        public string UserEmail { get; set; } = default!;

        /// <summary>
        /// Ticket status
        /// </summary>
        public string Status { get; set; } = default!;

        /// <summary>
        /// Priority level
        /// </summary>
        public string Priority { get; set; } = default!;

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Last updated date
        /// </summary>
        public DateTime? LastUpdatedDate { get; set; }

        /// <summary>
        /// Days since creation
        /// </summary>
        public int DaysSinceCreation { get; set; }
    }
}
