using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Service contract for car specification management operations
    /// </summary>
    public interface ICarSpecificationService
    {
        /// <summary>
        /// Creates a new car specification template
        /// </summary>
        /// <param name="request">The specification creation request</param>
        /// <returns>The created specification response</returns>
        Task<ApiResponse<CarSpecificationResponseDto>> CreateSpecificationAsync(CreateCarSpecificationRequest request);

        /// <summary>
        /// Updates an existing car specification template
        /// </summary>
        /// <param name="specificationId">The ID of the specification to update</param>
        /// <param name="request">The specification update request</param>
        /// <returns>The updated specification response</returns>
        Task<ApiResponse<CarSpecificationResponseDto>> UpdateSpecificationAsync(Guid specificationId, UpdateCarSpecificationRequest request);

        /// <summary>
        /// Gets a car specification by ID
        /// </summary>
        /// <param name="specificationId">The ID of the specification</param>
        /// <returns>The specification response</returns>
        Task<ApiResponse<CarSpecificationResponseDto>> GetSpecificationByIdAsync(Guid specificationId);

        /// <summary>
        /// Gets all car specifications
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive specifications</param>
        /// <returns>List of specification responses</returns>
        Task<ApiResponse<List<CarSpecificationResponseDto>>> GetSpecificationsAsync(bool includeInactive = false);

        /// <summary>
        /// Gets car specifications grouped by category
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive specifications</param>
        /// <returns>Grouped specification responses</returns>
        Task<ApiResponse<List<GroupedCarSpecificationResponseDto>>> GetSpecificationsGroupedByCategoryAsync(bool includeInactive = false);

        /// <summary>
        /// Deletes a car specification template (soft delete)
        /// </summary>
        /// <param name="specificationId">The ID of the specification to delete</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> DeleteSpecificationAsync(Guid specificationId);

        /// <summary>
        /// Activates or deactivates a car specification
        /// </summary>
        /// <param name="specificationId">The ID of the specification</param>
        /// <param name="isActive">Whether to activate or deactivate</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> ToggleSpecificationStatusAsync(Guid specificationId, bool isActive);

        /// <summary>
        /// Updates the display order of specifications
        /// </summary>
        /// <param name="specificationOrders">Dictionary of specification ID to display order</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<bool>> UpdateSpecificationDisplayOrderAsync(Dictionary<Guid, int> specificationOrders);

        /// <summary>
        /// Validates a specification value against its template
        /// </summary>
        /// <param name="specificationId">The ID of the specification template</param>
        /// <param name="value">The value to validate</param>
        /// <returns>Validation result</returns>
        Task<ApiResponse<SpecificationValidationResult>> ValidateSpecificationValueAsync(Guid specificationId, string value);
    }

    /// <summary>
    /// Result of specification value validation
    /// </summary>
    public class SpecificationValidationResult
    {
        /// <summary>
        /// Whether the value is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation error messages
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new List<string>();

        /// <summary>
        /// Suggested corrections or hints
        /// </summary>
        public List<string> Suggestions { get; set; } = new List<string>();
    }
}
