using System.Threading.Tasks;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl;

/// <summary>
/// Implementation of ICacheService that performs no caching
/// </summary>
public class NoCacheService : ICacheService
{
    public Task<T?> GetAsync<T>(string key) where T : class
    {
        return Task.FromResult<T?>(null);
    }

    public Task SetAsync<T>(string key, T value, int expirationMinutes = 30) where T : class
    {
        return Task.CompletedTask;
    }

    public Task RemoveAsync(string key)
    {
        return Task.CompletedTask;
    }

    public Task<bool> ExistsAsync(string key)
    {
        return Task.FromResult(false);
    }
} 