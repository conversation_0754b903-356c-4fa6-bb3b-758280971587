using System.ComponentModel.DataAnnotations;
using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Vendor
{
    /// <summary>
    /// DTO for reviewing vendor applications
    /// </summary>
    public class VendorApplicationReviewDto
    {
        /// <summary>
        /// The ID of the vendor application being reviewed
        /// </summary>
        [Required]
        public Guid ApplicationId { get; set; }
        
        /// <summary>
        /// The new status for the application
        /// </summary>
        [Required]
        public VendorApplicationStatus NewStatus { get; set; }
        
        /// <summary>
        /// Comments or notes from the reviewer
        /// </summary>
        public string? ReviewComments { get; set; }
        
        /// <summary>
        /// Flag indicating whether to generate credentials for an approved vendor
        /// </summary>
        public bool GenerateCredentials { get; set; } = true;
    }
}
