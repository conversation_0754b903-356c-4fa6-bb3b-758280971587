using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using System.Text.RegularExpressions;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Service implementation for car specification management operations
    /// </summary>
    public class CarSpecificationService : ICarSpecificationService
    {
        private readonly CarContext _context;
        private readonly ILogger<CarSpecificationService> _logger;

        public CarSpecificationService(CarContext context, ILogger<CarSpecificationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ApiResponse<CarSpecificationResponseDto>> CreateSpecificationAsync(CreateCarSpecificationRequest request)
        {
            try
            {
                // Check if specification with same name already exists
                var existingSpec = await _context.CarSpecifications
                    .Where(cs => cs.Name.ToLower() == request.Name.ToLower() && !cs.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingSpec != null)
                {
                    return ApiResponse<CarSpecificationResponseDto>.Failed("A specification with this name already exists", HttpStatusCode.Conflict);
                }

                var specification = new CarSpecification
                {
                    Name = request.Name,
                    Category = request.Category,
                    DataType = request.DataType,
                    Unit = request.Unit,
                    Description = request.Description,
                    IsRequired = request.IsRequired,
                    IsActive = request.IsActive,
                    DisplayOrder = request.DisplayOrder > 0 ? request.DisplayOrder : await GetNextDisplayOrderAsync(),
                    PredefinedOptions = request.PredefinedOptions != null && request.PredefinedOptions.Any() ? 
                        JsonSerializer.Serialize(request.PredefinedOptions) : null,
                    MinValue = request.MinValue,
                    MaxValue = request.MaxValue,
                    ValidationPattern = request.ValidationPattern,
                    HelpText = request.HelpText
                };

                _context.CarSpecifications.Add(specification);
                await _context.SaveChangesAsync();

                var specDto = MapToCarSpecificationResponseDto(specification);
                return ApiResponse<CarSpecificationResponseDto>.Success(specDto, "Specification created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating specification {Name}", request.Name);
                return ApiResponse<CarSpecificationResponseDto>.Failed("An error occurred while creating the specification", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<CarSpecificationResponseDto>> UpdateSpecificationAsync(Guid specificationId, UpdateCarSpecificationRequest request)
        {
            try
            {
                var specification = await _context.CarSpecifications
                    .Where(cs => cs.Id == specificationId && !cs.IsDeleted)
                    .FirstOrDefaultAsync();

                if (specification == null)
                {
                    return ApiResponse<CarSpecificationResponseDto>.Failed("Specification not found", HttpStatusCode.NotFound);
                }

                // Check if name is being changed and if it conflicts with existing specification
                if (!string.IsNullOrEmpty(request.Name) && request.Name.ToLower() != specification.Name.ToLower())
                {
                    var existingSpec = await _context.CarSpecifications
                        .Where(cs => cs.Name.ToLower() == request.Name.ToLower() && !cs.IsDeleted && cs.Id != specificationId)
                        .FirstOrDefaultAsync();

                    if (existingSpec != null)
                    {
                        return ApiResponse<CarSpecificationResponseDto>.Failed("A specification with this name already exists", HttpStatusCode.Conflict);
                    }

                    specification.Name = request.Name;
                }

                // Update properties if provided
                if (request.Category.HasValue) specification.Category = request.Category.Value;
                if (request.DataType.HasValue) specification.DataType = request.DataType.Value;
                if (request.Unit != null) specification.Unit = request.Unit;
                if (request.Description != null) specification.Description = request.Description;
                if (request.IsRequired.HasValue) specification.IsRequired = request.IsRequired.Value;
                if (request.IsActive.HasValue) specification.IsActive = request.IsActive.Value;
                if (request.DisplayOrder.HasValue) specification.DisplayOrder = request.DisplayOrder.Value;
                if (request.PredefinedOptions != null) 
                {
                    specification.PredefinedOptions = request.PredefinedOptions.Any() ? 
                        JsonSerializer.Serialize(request.PredefinedOptions) : null;
                }
                if (request.MinValue.HasValue) specification.MinValue = request.MinValue.Value;
                if (request.MaxValue.HasValue) specification.MaxValue = request.MaxValue.Value;
                if (request.ValidationPattern != null) specification.ValidationPattern = request.ValidationPattern;
                if (request.HelpText != null) specification.HelpText = request.HelpText;

                await _context.SaveChangesAsync();

                var specDto = MapToCarSpecificationResponseDto(specification);
                return ApiResponse<CarSpecificationResponseDto>.Success(specDto, "Specification updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating specification {SpecificationId}", specificationId);
                return ApiResponse<CarSpecificationResponseDto>.Failed("An error occurred while updating the specification", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<CarSpecificationResponseDto>> GetSpecificationByIdAsync(Guid specificationId)
        {
            try
            {
                var specification = await _context.CarSpecifications
                    .Where(cs => cs.Id == specificationId && !cs.IsDeleted)
                    .FirstOrDefaultAsync();

                if (specification == null)
                {
                    return ApiResponse<CarSpecificationResponseDto>.Failed("Specification not found", HttpStatusCode.NotFound);
                }

                var specDto = MapToCarSpecificationResponseDto(specification);
                return ApiResponse<CarSpecificationResponseDto>.Success(specDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting specification {SpecificationId}", specificationId);
                return ApiResponse<CarSpecificationResponseDto>.Failed("An error occurred while retrieving the specification", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<List<CarSpecificationResponseDto>>> GetSpecificationsAsync(bool includeInactive = false)
        {
            try
            {
                var query = _context.CarSpecifications.Where(cs => !cs.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(cs => cs.IsActive);
                }

                var specifications = await query
                    .OrderBy(cs => cs.DisplayOrder)
                    .ThenBy(cs => cs.Name)
                    .ToListAsync();

                var specDtos = specifications.Select(MapToCarSpecificationResponseDto).ToList();
                return ApiResponse<List<CarSpecificationResponseDto>>.Success(specDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting specifications");
                return ApiResponse<List<CarSpecificationResponseDto>>.Failed("An error occurred while retrieving specifications", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<List<GroupedCarSpecificationResponseDto>>> GetSpecificationsGroupedByCategoryAsync(bool includeInactive = false)
        {
            try
            {
                var query = _context.CarSpecifications.Where(cs => !cs.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(cs => cs.IsActive);
                }

                var specifications = await query
                    .OrderBy(cs => cs.Category)
                    .ThenBy(cs => cs.DisplayOrder)
                    .ThenBy(cs => cs.Name)
                    .ToListAsync();

                var groupedSpecs = specifications
                    .GroupBy(cs => cs.Category)
                    .Select(g => new GroupedCarSpecificationResponseDto
                    {
                        Category = g.Key,
                        CategoryName = g.Key.ToString(),
                        Specifications = g.Select(MapToCarSpecificationResponseDto).ToList()
                    })
                    .ToList();

                return ApiResponse<List<GroupedCarSpecificationResponseDto>>.Success(groupedSpecs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting grouped specifications");
                return ApiResponse<List<GroupedCarSpecificationResponseDto>>.Failed("An error occurred while retrieving grouped specifications", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> DeleteSpecificationAsync(Guid specificationId)
        {
            try
            {
                var specification = await _context.CarSpecifications
                    .Where(cs => cs.Id == specificationId && !cs.IsDeleted)
                    .FirstOrDefaultAsync();

                if (specification == null)
                {
                    return ApiResponse<bool>.Failed("Specification not found", HttpStatusCode.NotFound);
                }

                // Check if specification is being used by any cars
                var usageCount = await _context.CarSpecificationValues
                    .Where(csv => csv.CarSpecificationId == specificationId)
                    .CountAsync();

                if (usageCount > 0)
                {
                    return ApiResponse<bool>.Failed($"Cannot delete specification. It is being used by {usageCount} car(s)", HttpStatusCode.Conflict);
                }

                specification.IsDeleted = true;
                specification.DeletedOn = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Specification deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting specification {SpecificationId}", specificationId);
                return ApiResponse<bool>.Failed("An error occurred while deleting the specification", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> ToggleSpecificationStatusAsync(Guid specificationId, bool isActive)
        {
            try
            {
                var specification = await _context.CarSpecifications
                    .Where(cs => cs.Id == specificationId && !cs.IsDeleted)
                    .FirstOrDefaultAsync();

                if (specification == null)
                {
                    return ApiResponse<bool>.Failed("Specification not found", HttpStatusCode.NotFound);
                }

                specification.IsActive = isActive;
                await _context.SaveChangesAsync();

                var action = isActive ? "activated" : "deactivated";
                return ApiResponse<bool>.Success(true, $"Specification {action} successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while toggling specification {SpecificationId} status", specificationId);
                return ApiResponse<bool>.Failed("An error occurred while updating the specification status", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> UpdateSpecificationDisplayOrderAsync(Dictionary<Guid, int> specificationOrders)
        {
            try
            {
                foreach (var order in specificationOrders)
                {
                    var specification = await _context.CarSpecifications.FindAsync(order.Key);
                    if (specification != null && !specification.IsDeleted)
                    {
                        specification.DisplayOrder = order.Value;
                    }
                }

                await _context.SaveChangesAsync();
                return ApiResponse<bool>.Success(true, "Display order updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating specification display order");
                return ApiResponse<bool>.Failed("An error occurred while updating display order", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<SpecificationValidationResult>> ValidateSpecificationValueAsync(Guid specificationId, string value)
        {
            try
            {
                var specification = await _context.CarSpecifications
                    .Where(cs => cs.Id == specificationId && !cs.IsDeleted)
                    .FirstOrDefaultAsync();

                if (specification == null)
                {
                    return ApiResponse<SpecificationValidationResult>.Failed("Specification not found", HttpStatusCode.NotFound);
                }

                var result = new SpecificationValidationResult { IsValid = true };

                // Validate based on data type
                switch (specification.DataType)
                {
                    case SpecificationDataType.Number:
                        if (!decimal.TryParse(value, out var numericValue))
                        {
                            result.IsValid = false;
                            result.ErrorMessages.Add("Value must be a valid number");
                        }
                        else
                        {
                            if (specification.MinValue.HasValue && numericValue < specification.MinValue.Value)
                            {
                                result.IsValid = false;
                                result.ErrorMessages.Add($"Value must be at least {specification.MinValue.Value}");
                            }
                            if (specification.MaxValue.HasValue && numericValue > specification.MaxValue.Value)
                            {
                                result.IsValid = false;
                                result.ErrorMessages.Add($"Value must be at most {specification.MaxValue.Value}");
                            }
                        }
                        break;

                    case SpecificationDataType.Boolean:
                        if (!bool.TryParse(value, out _) && !new[] { "yes", "no", "true", "false", "1", "0" }.Contains(value.ToLower()))
                        {
                            result.IsValid = false;
                            result.ErrorMessages.Add("Value must be a valid boolean (true/false, yes/no, 1/0)");
                        }
                        break;

                    case SpecificationDataType.Date:
                        if (!DateTime.TryParse(value, out _))
                        {
                            result.IsValid = false;
                            result.ErrorMessages.Add("Value must be a valid date");
                        }
                        break;

                    case SpecificationDataType.SingleSelect:
                        if (!string.IsNullOrEmpty(specification.PredefinedOptions))
                        {
                            var options = JsonSerializer.Deserialize<List<string>>(specification.PredefinedOptions);
                            if (options != null && !options.Contains(value, StringComparer.OrdinalIgnoreCase))
                            {
                                result.IsValid = false;
                                result.ErrorMessages.Add($"Value must be one of: {string.Join(", ", options)}");
                                result.Suggestions.AddRange(options);
                            }
                        }
                        break;

                    case SpecificationDataType.MultiSelect:
                        if (!string.IsNullOrEmpty(specification.PredefinedOptions))
                        {
                            var options = JsonSerializer.Deserialize<List<string>>(specification.PredefinedOptions);
                            var selectedValues = value.Split(',').Select(v => v.Trim()).ToList();
                            
                            if (options != null)
                            {
                                var invalidValues = selectedValues.Where(v => !options.Contains(v, StringComparer.OrdinalIgnoreCase)).ToList();
                                if (invalidValues.Any())
                                {
                                    result.IsValid = false;
                                    result.ErrorMessages.Add($"Invalid values: {string.Join(", ", invalidValues)}. Valid options are: {string.Join(", ", options)}");
                                    result.Suggestions.AddRange(options);
                                }
                            }
                        }
                        break;

                    case SpecificationDataType.Url:
                        if (!Uri.TryCreate(value, UriKind.Absolute, out _))
                        {
                            result.IsValid = false;
                            result.ErrorMessages.Add("Value must be a valid URL");
                        }
                        break;
                }

                // Validate against regex pattern if provided
                if (!string.IsNullOrEmpty(specification.ValidationPattern))
                {
                    try
                    {
                        if (!Regex.IsMatch(value, specification.ValidationPattern))
                        {
                            result.IsValid = false;
                            result.ErrorMessages.Add("Value does not match the required pattern");
                        }
                    }
                    catch (Exception)
                    {
                        // Invalid regex pattern - log but don't fail validation
                        _logger.LogWarning("Invalid regex pattern for specification {SpecificationId}: {Pattern}", specificationId, specification.ValidationPattern);
                    }
                }

                return ApiResponse<SpecificationValidationResult>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while validating specification value for {SpecificationId}", specificationId);
                return ApiResponse<SpecificationValidationResult>.Failed("An error occurred while validating the value", HttpStatusCode.InternalServerError);
            }
        }

        private async Task<int> GetNextDisplayOrderAsync()
        {
            var maxOrder = await _context.CarSpecifications
                .Where(cs => !cs.IsDeleted)
                .MaxAsync(cs => (int?)cs.DisplayOrder) ?? 0;
            
            return maxOrder + 1;
        }

        private CarSpecificationResponseDto MapToCarSpecificationResponseDto(CarSpecification specification)
        {
            return new CarSpecificationResponseDto
            {
                Id = specification.Id,
                Name = specification.Name,
                Category = specification.Category,
                CategoryName = specification.Category.ToString(),
                DataType = specification.DataType,
                DataTypeName = specification.DataType.ToString(),
                Unit = specification.Unit,
                Description = specification.Description,
                IsRequired = specification.IsRequired,
                IsActive = specification.IsActive,
                DisplayOrder = specification.DisplayOrder,
                PredefinedOptions = !string.IsNullOrEmpty(specification.PredefinedOptions) ? 
                    JsonSerializer.Deserialize<List<string>>(specification.PredefinedOptions) : null,
                MinValue = specification.MinValue,
                MaxValue = specification.MaxValue,
                ValidationPattern = specification.ValidationPattern,
                HelpText = specification.HelpText,
                CreatedOn = specification.CreatedOn,
                ModifiedOn = specification.ModifiedOn
            };
        }
    }
}
