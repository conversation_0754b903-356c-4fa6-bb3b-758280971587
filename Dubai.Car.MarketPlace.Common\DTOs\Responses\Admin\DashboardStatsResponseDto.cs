namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for dashboard statistics
    /// </summary>
    public class DashboardStatsResponseDto
    {
        /// <summary>
        /// Total number of users in the system
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// Percentage change from last month for total users
        /// </summary>
        public decimal TotalUsersChangePercentage { get; set; }

        /// <summary>
        /// Number of active vendors
        /// </summary>
        public int ActiveVendors { get; set; }

        /// <summary>
        /// Number of vendors pending approval
        /// </summary>
        public int PendingVendors { get; set; }

        /// <summary>
        /// Total number of listings in the system
        /// </summary>
        public int TotalListings { get; set; }

        /// <summary>
        /// Percentage change from last month for total listings
        /// </summary>
        public decimal TotalListingsChangePercentage { get; set; }

        /// <summary>
        /// Platform revenue for current month
        /// </summary>
        public decimal PlatformRevenue { get; set; }

        /// <summary>
        /// Percentage change from last month for platform revenue
        /// </summary>
        public decimal PlatformRevenueChangePercentage { get; set; }

        /// <summary>
        /// Platform revenue for last month
        /// </summary>
        public decimal LastMonthRevenue { get; set; }
    }
}
