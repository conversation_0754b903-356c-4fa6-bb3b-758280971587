﻿using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Dubai.Car.MarketPlace.Logic.Services.Impl;
using StackExchange.Redis;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Provides extension methods for configuring caching services in an application.
    /// </summary>
    /// <remarks>This class includes methods to configure caching settings, such as enabling or disabling
    /// caching, setting up Redis as the caching provider, and registering related services like cache invalidation and
    /// cache warming. It is designed to integrate caching functionality into an application's service collection and
    /// builder.</remarks>
    public static class ConfigureCacheExtension
    {
        /// <summary>
        /// Configures caching services for the application based on the provided configuration.
        /// </summary>
        /// <remarks>This method sets up caching services based on the application's configuration. If
        /// caching is enabled, it configures Redis as the caching provider,  registers related services, and adds a
        /// health check for Redis. If caching is disabled, it registers no-cache services to ensure the application 
        /// operates without caching.  When Redis caching is enabled, the method also supports optional cache warming if
        /// specified in the configuration.</remarks>
        /// <param name="services">The <see cref="IServiceCollection"/> to which caching services will be added.</param>
        /// <param name="config">The <see cref="IConfiguration"/> instance containing application settings.</param>
        /// <param name="builder">The <see cref="WebApplicationBuilder"/> used to configure the application's services.</param>
        /// <param name="configuration">The <see cref="ConfigurationManager"/> used to retrieve connection strings and other configuration values.</param>
        private static void ConfigureCache(this IServiceCollection services, IConfiguration config, WebApplicationBuilder builder, ConfigurationManager configuration)
        {
            // Configure cache settings
            var cacheConfig = config.GetSection("Cache").Get<CacheConfiguration>() ?? new CacheConfiguration();
            services.AddSingleton(cacheConfig);

            if (cacheConfig.EnableCaching)
            {
                // Configure Redis connection
                var redisConnection = ConnectionMultiplexer.Connect(configuration.GetConnectionString("Redis")!);
                builder.Services.AddSingleton<IConnectionMultiplexer>(redisConnection);

                // Configure Redis cache
                builder.Services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = configuration.GetConnectionString("Redis");
                    options.InstanceName = "DubaiCarMarketPlace";
                });

                // Register Redis cache service
                builder.Services.AddScoped<ICacheService, RedisCacheService>();

                // Register cache invalidation service
                builder.Services.AddScoped<CacheInvalidationService>();

                // Add Redis health check
                builder.Services.AddHealthChecks()
                    .AddRedis(
                        redisConnection,
                        name: "redis",
                        tags: new[] { "cache", "redis" },
                        timeout: TimeSpan.FromSeconds(5)
                    );

                // Register cache warming service if enabled
                if (cacheConfig.EnableCacheWarming)
                {
                    builder.Services.AddHostedService<CacheWarmingService>();
                }
            }
            else
            {
                // Register no-cache service when caching is disabled
                builder.Services.AddScoped<ICacheService, NoCacheService>();

                // Register no-op cache invalidation service
                builder.Services.AddScoped<CacheInvalidationService>();
            }
        }
    }
}
