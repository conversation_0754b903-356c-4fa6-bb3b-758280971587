﻿using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    ///  Base Controller
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        private readonly string? _userId;

        /// <summary>
        /// This property gets the user ID and other user properties from the claims of the current user.
        /// </summary>
        public BaseController()
        {
            _userId = User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }
    }
}
