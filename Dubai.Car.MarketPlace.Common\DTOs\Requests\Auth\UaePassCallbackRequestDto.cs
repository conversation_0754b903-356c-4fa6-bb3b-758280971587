using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth
{
    /// <summary>
    /// Request DTO for UAE PASS OAuth callback
    /// </summary>
    public class UaePassCallbackRequestDto
    {
        /// <summary>
        /// Authorization code returned by UAE PASS
        /// </summary>
        [Required(ErrorMessage = "Authorization code is required")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// State parameter for CSRF protection
        /// </summary>
        public string? State { get; set; }

        /// <summary>
        /// Error code if authorization failed
        /// </summary>
        public string? Error { get; set; }

        /// <summary>
        /// Error description if authorization failed
        /// </summary>
        public string? ErrorDescription { get; set; }
    }
}
