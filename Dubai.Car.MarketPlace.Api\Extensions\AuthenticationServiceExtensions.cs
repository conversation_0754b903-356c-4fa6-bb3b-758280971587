﻿using Dubai.Car.MarketPlace.Common.Configurations;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Dubai.Car.MarketPlace.Api.Extensions
{
    /// <summary>
    /// Add authentication class
    /// </summary>
    public static class AuthenticationServiceExtensions
    {
        /// <summary>
        /// AddAuthenticationConfiguring method
        /// </summary>
        /// <param name="services"></param>
        /// <param name="config"></param>
        public static void AddJwtAuthentication(this IServiceCollection services, IConfiguration config)
        {
            // Bind the JwtCredentials section to the JwtCredentials class
            var jwt = new JwtSettings();
            config.GetSection("JwtCredentials").Bind(jwt);

            //For production
            //var key = Environment.GetEnvironmentVariable("KEY");

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            }).AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,

                    ValidIssuer = jwt.Issuer,
                    ValidAudience = jwt.Audience,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwt.SecretKey)),
                    ClockSkew = TimeSpan.Zero
                };
            });
        }
    }
}
