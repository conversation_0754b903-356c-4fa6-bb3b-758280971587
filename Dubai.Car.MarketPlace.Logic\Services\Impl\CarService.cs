using Dubai.Car.MarketPlace.Common.Constants;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Car;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using WatchDog;
using static Dubai.Car.MarketPlace.Common.Enums.CarEnums;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Service implementation for car management operations
    /// </summary>
    public class CarService : ICarService
    {
        private readonly CarContext _context;
        private readonly ILogger<CarService> _logger;
        private readonly IAwsService _awsService;
        private readonly UserManager<User> _userManager;

        public CarService(CarContext context, ILogger<CarService> logger, IAwsService awsService, UserManager<User> userManager)
        {
            _context = context;
            _logger = logger;
            _awsService = awsService;
            _userManager = userManager;
        }

        public async Task<ApiResponse<CarResponseDto>> CreateCarAsync(CreateCarRequest request, Guid userId)
        {
            try
            {
                // Validate foreign keys
                var brand = await _context.Brands.FindAsync(request.BrandId);
                if (brand == null || !brand.IsActive)
                {
                    return ApiResponse<CarResponseDto>.Failed("Invalid brand selected", HttpStatusCode.BadRequest);
                }

                var bodyType = await _context.BodyTypes.FindAsync(request.BodyTypeId);
                if (bodyType == null || !bodyType.IsActive)
                {
                    return ApiResponse<CarResponseDto>.Failed("Invalid body type selected", HttpStatusCode.BadRequest);
                }

                var fuelType = await _context.FuelTypes.FindAsync(request.FuelTypeId);
                if (fuelType == null || !fuelType.IsActive)
                {
                    return ApiResponse<CarResponseDto>.Failed("Invalid fuel type selected", HttpStatusCode.BadRequest);
                }

                // Validate vendor if provided
                if (request.VendorId.HasValue)
                {
                    var vendor = await _context.Vendors.FindAsync(request.VendorId.Value);
                    if (vendor == null || vendor.UserId != userId)
                    {
                        return ApiResponse<CarResponseDto>.Failed("Invalid vendor or unauthorized access", HttpStatusCode.BadRequest);
                    }
                }

                var car = new Data.Entities.Car
                {
                    Model = request.Model,
                    Year = request.Year,
                    Price = request.Price,
                    Mileage = request.Mileage,
                    Color = request.Color,
                    VIN = request.VIN,
                    LicensePlate = request.LicensePlate,
                    EngineSize = request.EngineSize,
                    Transmission = request.Transmission,
                    NumberOfDoors = request.NumberOfDoors,
                    NumberOfSeats = request.NumberOfSeats,
                    Condition = request.Condition,
                    Status = CarStatus.Draft,
                    Description = request.Description,
                    Features = request.Features != null ? JsonSerializer.Serialize(request.Features) : null,
                    IsFeatured = request.IsFeatured,
                    ExpiryDate = request.ExpiryDate,
                    BrandId = request.BrandId,
                    BodyTypeId = request.BodyTypeId,
                    FuelTypeId = request.FuelTypeId,
                    VendorId = request.VendorId,
                    UserId = userId
                };

                _context.Cars.Add(car);
                await _context.SaveChangesAsync();

                // Add specification values if provided
                if (request.SpecificationValues != null && request.SpecificationValues.Any())
                {
                    foreach (var specValue in request.SpecificationValues)
                    {
                        var carSpecValue = new CarSpecificationValue
                        {
                            CarId = car.Id,
                            CarSpecificationId = specValue.CarSpecificationId,
                            Value = specValue.Value,
                            Notes = specValue.Notes
                        };
                        _context.CarSpecificationValues.Add(carSpecValue);
                    }
                    await _context.SaveChangesAsync();
                }

                // Handle optional images
                if (request.Images != null && request.Images.Any())
                {
                    var uploadResult = await UploadCarImagesInternalAsync(request.Images, request.PrimaryImageIndex, car.Id);

                    if (!string.IsNullOrEmpty(uploadResult.ErrorMessage))
                    {
                        return ApiResponse<CarResponseDto>.Failed(uploadResult.ErrorMessage, HttpStatusCode.BadRequest);
                    }

                    // Update car with images and set status based on user role
                    car.ImageKeys = JsonSerializer.Serialize(uploadResult.ImageKeys);
                    car.PrimaryImageKey = uploadResult.PrimaryImageKey;

                    // Check if user is admin to determine status
                    var isAdmin = await IsUserAdminAsync(userId);
                    car.Status = isAdmin ? CarStatus.Active : CarStatus.PendingApproval;

                    await _context.SaveChangesAsync();

                    WatchLogger.Log($"Successfully uploaded {uploadResult.ImageKeys.Count} images for new car {car.Id}. Status: {car.Status}", "CreateCarAsync");
                }

                // Load the created car with related data
                var createdCar = await GetCarWithIncludesAsync(car.Id);
                var carDto = MapToCarResponseDto(createdCar!);

                return ApiResponse<CarResponseDto>.Success(carDto, "Car created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating car for user {UserId}", userId);
                return ApiResponse<CarResponseDto>.Failed("An error occurred while creating the car", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<CarResponseDto>> UpdateCarAsync(Guid carId, UpdateCarRequest request, Guid userId)
        {
            try
            {
                var car = await _context.Cars
                    .Include(c => c.SpecificationValues)
                    .Where(c => c.Id == carId && !c.IsDeleted)
                    .FirstOrDefaultAsync();

                if (car == null)
                {
                    return ApiResponse<CarResponseDto>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                // Check if user has permission to update this car
                if (car.UserId != userId)
                {
                    // Check if user is a vendor and owns this car
                    var vendor = await _context.Vendors.FirstOrDefaultAsync(v => v.UserId == userId && v.Id == car.VendorId);
                    if (vendor == null)
                    {
                        return ApiResponse<CarResponseDto>.Failed("Unauthorized to update this car", HttpStatusCode.Forbidden);
                    }
                }

                // Update properties if provided
                if (!string.IsNullOrEmpty(request.Model)) car.Model = request.Model;
                if (request.Year.HasValue) car.Year = request.Year.Value;
                if (request.Price.HasValue) car.Price = request.Price.Value;
                if (request.Mileage.HasValue) car.Mileage = request.Mileage.Value;
                if (request.Color != null) car.Color = request.Color;
                if (request.VIN != null) car.VIN = request.VIN;
                if (request.LicensePlate != null) car.LicensePlate = request.LicensePlate;
                if (request.EngineSize.HasValue) car.EngineSize = request.EngineSize.Value;
                if (request.Transmission.HasValue) car.Transmission = request.Transmission.Value;
                if (request.NumberOfDoors.HasValue) car.NumberOfDoors = request.NumberOfDoors.Value;
                if (request.NumberOfSeats.HasValue) car.NumberOfSeats = request.NumberOfSeats.Value;
                if (request.Condition.HasValue) car.Condition = request.Condition.Value;
                if (request.Status.HasValue) car.Status = request.Status.Value;
                if (request.Description != null) car.Description = request.Description;
                if (request.Features != null) car.Features = JsonSerializer.Serialize(request.Features);
                if (request.IsFeatured.HasValue) car.IsFeatured = request.IsFeatured.Value;
                if (request.IsSold.HasValue)
                {
                    car.IsSold = request.IsSold.Value;
                    if (request.IsSold.Value) car.SoldDate = DateTime.UtcNow;
                }
                if (request.ExpiryDate.HasValue) car.ExpiryDate = request.ExpiryDate.Value;
                if (request.BrandId.HasValue) car.BrandId = request.BrandId.Value;
                if (request.BodyTypeId.HasValue) car.BodyTypeId = request.BodyTypeId.Value;
                if (request.FuelTypeId.HasValue) car.FuelTypeId = request.FuelTypeId.Value;
                if (request.VendorId.HasValue) car.VendorId = request.VendorId.Value;

                // Update specification values if provided
                if (request.SpecificationValues != null)
                {
                    // Remove existing specification values that are marked for deletion
                    var toDelete = request.SpecificationValues.Where(sv => sv.IsDeleted && sv.Id.HasValue).ToList();
                    foreach (var deleteSpec in toDelete)
                    {
                        var existingSpec = car.SpecificationValues.FirstOrDefault(sv => sv.Id == deleteSpec.Id.Value);
                        if (existingSpec != null)
                        {
                            _context.CarSpecificationValues.Remove(existingSpec);
                        }
                    }

                    // Update or add specification values
                    var toUpdate = request.SpecificationValues.Where(sv => !sv.IsDeleted).ToList();
                    foreach (var specValue in toUpdate)
                    {
                        if (specValue.Id.HasValue)
                        {
                            // Update existing
                            var existingSpec = car.SpecificationValues.FirstOrDefault(sv => sv.Id == specValue.Id.Value);
                            if (existingSpec != null)
                            {
                                existingSpec.Value = specValue.Value;
                                existingSpec.Notes = specValue.Notes;
                            }
                        }
                        else
                        {
                            // Add new
                            var newSpec = new CarSpecificationValue
                            {
                                CarId = car.Id,
                                CarSpecificationId = specValue.CarSpecificationId,
                                Value = specValue.Value,
                                Notes = specValue.Notes
                            };
                            _context.CarSpecificationValues.Add(newSpec);
                        }
                    }
                }

                // Handle optional images (will replace existing images if provided)
                if (request.Images != null && request.Images.Any())
                {
                    // Get existing image keys to delete them later
                    var existingImageKeys = new List<string>();
                    if (!string.IsNullOrEmpty(car.ImageKeys))
                    {
                        try
                        {
                            existingImageKeys = JsonSerializer.Deserialize<List<string>>(car.ImageKeys) ?? new List<string>();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to deserialize existing image keys for car {CarId}", carId);
                        }
                    }

                    // Upload new images using the helper method
                    var uploadResult = await UploadCarImagesInternalAsync(request.Images, request.PrimaryImageIndex, carId);

                    if (!string.IsNullOrEmpty(uploadResult.ErrorMessage))
                    {
                        return ApiResponse<CarResponseDto>.Failed(uploadResult.ErrorMessage, HttpStatusCode.BadRequest);
                    }

                    // Update car with new images
                    car.ImageKeys = JsonSerializer.Serialize(uploadResult.ImageKeys);
                    car.PrimaryImageKey = uploadResult.PrimaryImageKey;

                    // Set status to PendingApproval if not admin (unless already active/approved)
                    if (car.Status == CarStatus.Draft)
                    {
                        var isAdmin = await IsUserAdminAsync(userId);
                        car.Status = isAdmin ? CarStatus.Active : CarStatus.PendingApproval;
                    }

                    await _context.SaveChangesAsync();

                    // Delete old images from S3 (do this after successful database update)
                    foreach (var oldImageKey in existingImageKeys)
                    {
                        try
                        {
                            await _awsService.DeleteFileAsync(oldImageKey);
                            WatchLogger.Log($"Deleted old image {oldImageKey} for car {carId}", "UpdateCarAsync");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to delete old image {ImageKey} for car {CarId}", oldImageKey, carId);
                            // Don't fail the entire operation if old image deletion fails
                        }
                    }

                    WatchLogger.Log($"Successfully updated {uploadResult.ImageKeys.Count} images for car {carId}. Status: {car.Status}", "UpdateCarAsync");
                }
                else
                {
                    await _context.SaveChangesAsync();
                }

                // Load the updated car with related data
                var updatedCar = await GetCarWithIncludesAsync(car.Id);
                var carDto = MapToCarResponseDto(updatedCar!);

                return ApiResponse<CarResponseDto>.Success(carDto, "Car updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating car {CarId} for user {UserId}", carId, userId);
                return ApiResponse<CarResponseDto>.Failed("An error occurred while updating the car", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<CarResponseDto>> GetCarByIdAsync(Guid carId, Guid? userId = null)
        {
            try
            {
                var car = await GetCarWithIncludesAsync(carId);

                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<CarResponseDto>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                // Increment view count if user is provided and different from car owner
                if (userId.HasValue && userId.Value != car.UserId)
                {
                    car.ViewCount++;
                    await _context.SaveChangesAsync();
                }

                var carDto = MapToCarResponseDto(car);
                return ApiResponse<CarResponseDto>.Success(carDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting car {CarId}", carId);
                return ApiResponse<CarResponseDto>.Failed("An error occurred while retrieving the car", HttpStatusCode.InternalServerError);
            }
        }

        private async Task<Data.Entities.Car?> GetCarWithIncludesAsync(Guid carId)
        {
            return await _context.Cars
                .Include(c => c.Brand)
                .Include(c => c.BodyType)
                .Include(c => c.FuelType)
                .Include(c => c.Vendor)
                .Include(c => c.User)
                .Include(c => c.SpecificationValues)
                    .ThenInclude(sv => sv.CarSpecification)
                .Where(c => c.Id == carId)
                .FirstOrDefaultAsync();
        }

        private CarResponseDto MapToCarResponseDto(Data.Entities.Car car)
        {
            return new CarResponseDto
            {
                Id = car.Id,
                Model = car.Model,
                Year = car.Year,
                Price = car.Price,
                Mileage = car.Mileage,
                Color = car.Color,
                VIN = car.VIN,
                LicensePlate = car.LicensePlate,
                EngineSize = car.EngineSize,
                Transmission = car.Transmission,
                NumberOfDoors = car.NumberOfDoors,
                NumberOfSeats = car.NumberOfSeats,
                Condition = car.Condition,
                Status = car.Status,
                Description = car.Description,
                Features = !string.IsNullOrEmpty(car.Features) ? JsonSerializer.Deserialize<List<string>>(car.Features) : null,
                ImageUrls = !string.IsNullOrEmpty(car.ImageKeys) ?
                    JsonSerializer.Deserialize<List<string>>(car.ImageKeys)?.Select(key => _awsService.GetPresignedUrlAsync(key).Result).ToList() : null,
                PrimaryImageUrl = !string.IsNullOrEmpty(car.PrimaryImageKey) ? _awsService.GetPresignedUrlAsync(car.PrimaryImageKey).Result : null,
                IsFeatured = car.IsFeatured,
                IsSold = car.IsSold,
                SoldDate = car.SoldDate,
                ViewCount = car.ViewCount,
                InquiryCount = car.InquiryCount,
                ExpiryDate = car.ExpiryDate,
                CreatedOn = car.CreatedOn,
                ModifiedOn = car.ModifiedOn,
                Brand = new BrandResponseDto
                {
                    Id = car.Brand.Id,
                    Name = car.Brand.Name,
                    Description = car.Brand.Description,
                    LogoImageUrl = !string.IsNullOrEmpty(car.Brand.LogoImageKey) ? _awsService.GetPresignedUrlAsync(car.Brand.LogoImageKey).Result : null,
                    CountryOfOrigin = car.Brand.CountryOfOrigin,
                    IsActive = car.Brand.IsActive,
                    DisplayOrder = car.Brand.DisplayOrder
                },
                BodyType = new BodyTypeResponseDto
                {
                    Id = car.BodyType.Id,
                    Name = car.BodyType.Name,
                    Description = car.BodyType.Description,
                    IconImageUrl = !string.IsNullOrEmpty(car.BodyType.IconImageKey) ? _awsService.GetPresignedUrlAsync(car.BodyType.IconImageKey).Result : null,
                    IsActive = car.BodyType.IsActive,
                    DisplayOrder = car.BodyType.DisplayOrder
                },
                FuelType = new FuelTypeResponseDto
                {
                    Id = car.FuelType.Id,
                    Name = car.FuelType.Name,
                    Description = car.FuelType.Description,
                    IconImageUrl = !string.IsNullOrEmpty(car.FuelType.IconImageKey) ? _awsService.GetPresignedUrlAsync(car.FuelType.IconImageKey).Result : null,
                    IsActive = car.FuelType.IsActive,
                    DisplayOrder = car.FuelType.DisplayOrder,
                    EnvironmentalRating = car.FuelType.EnvironmentalRating
                },
                Vendor = car.Vendor != null ? new VendorResponseDto
                {
                    Id = car.Vendor.Id,
                    BusinessName = car.Vendor.BusinessName,
                    ContactPersonName = car.Vendor.ContactPersonName,
                    BusinessEmail = car.Vendor.BusinessEmail,
                    PhoneNumber = car.Vendor.PhoneNumber,
                    City = car.Vendor.City,
                    LogoImageUrl = !string.IsNullOrEmpty(car.Vendor.LogoImageKey) ? _awsService.GetPresignedUrlAsync(car.Vendor.LogoImageKey).Result : null,
                    Rating = car.Vendor.Rating,
                    TotalRatings = car.Vendor.TotalRatings,
                    IsVerified = car.Vendor.IsVerified,
                    IsFeatured = car.Vendor.IsFeatured
                } : null,
                User = new UserResponseDto
                {
                    Id = car.User.Id,
                    FirstName = car.User.FirstName,
                    LastName = car.User.LastName,
                    Email = car.User.Email,
                    PhoneNumber = car.User.PhoneNumber
                },
                SpecificationValues = car.SpecificationValues?.Select(sv => new CarSpecificationValueResponseDto
                {
                    Id = sv.Id,
                    Value = sv.Value,
                    Notes = sv.Notes,
                    CreatedOn = sv.CreatedOn,
                    ModifiedOn = sv.ModifiedOn,
                    CarSpecification = new CarSpecificationResponseDto
                    {
                        Id = sv.CarSpecification.Id,
                        Name = sv.CarSpecification.Name,
                        Category = sv.CarSpecification.Category,
                        CategoryName = sv.CarSpecification.Category.ToString(),
                        DataType = sv.CarSpecification.DataType,
                        DataTypeName = sv.CarSpecification.DataType.ToString(),
                        Unit = sv.CarSpecification.Unit,
                        Description = sv.CarSpecification.Description,
                        IsRequired = sv.CarSpecification.IsRequired,
                        IsActive = sv.CarSpecification.IsActive,
                        DisplayOrder = sv.CarSpecification.DisplayOrder,
                        PredefinedOptions = !string.IsNullOrEmpty(sv.CarSpecification.PredefinedOptions) ?
                            JsonSerializer.Deserialize<List<string>>(sv.CarSpecification.PredefinedOptions) : null,
                        MinValue = sv.CarSpecification.MinValue,
                        MaxValue = sv.CarSpecification.MaxValue,
                        ValidationPattern = sv.CarSpecification.ValidationPattern,
                        HelpText = sv.CarSpecification.HelpText,
                        CreatedOn = sv.CarSpecification.CreatedOn,
                        ModifiedOn = sv.CarSpecification.ModifiedOn
                    }
                }).ToList()
            };
        }

        public async Task<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>> GetCarsAsync(CarFilterRequest request)
        {
            try
            {
                var query = _context.Cars
                    .Include(c => c.Brand)
                    .Include(c => c.BodyType)
                    .Include(c => c.FuelType)
                    .Include(c => c.Vendor)
                    .Where(c => !c.IsDeleted);

                if (!request.IncludeDeleted)
                {
                    query = query.Where(c => !c.IsDeleted);
                }

                // Apply filters
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    query = query.Where(c => c.Model.Contains(request.SearchTerm) ||
                                           c.Brand.Name.Contains(request.SearchTerm) ||
                                           c.Description!.Contains(request.SearchTerm));
                }

                if (request.BrandIds != null && request.BrandIds.Any())
                {
                    query = query.Where(c => request.BrandIds.Contains(c.BrandId));
                }

                if (request.BodyTypeIds != null && request.BodyTypeIds.Any())
                {
                    query = query.Where(c => request.BodyTypeIds.Contains(c.BodyTypeId));
                }

                if (request.FuelTypeIds != null && request.FuelTypeIds.Any())
                {
                    query = query.Where(c => request.FuelTypeIds.Contains(c.FuelTypeId));
                }

                if (request.VendorId.HasValue)
                {
                    query = query.Where(c => c.VendorId == request.VendorId.Value);
                }

                if (request.Conditions != null && request.Conditions.Any())
                {
                    query = query.Where(c => request.Conditions.Contains(c.Condition));
                }

                if (request.Statuses != null && request.Statuses.Any())
                {
                    query = query.Where(c => request.Statuses.Contains(c.Status));
                }

                if (request.TransmissionTypes != null && request.TransmissionTypes.Any())
                {
                    query = query.Where(c => c.Transmission.HasValue && request.TransmissionTypes.Contains(c.Transmission.Value));
                }

                if (request.MinPrice.HasValue)
                {
                    query = query.Where(c => c.Price >= request.MinPrice.Value);
                }

                if (request.MaxPrice.HasValue)
                {
                    query = query.Where(c => c.Price <= request.MaxPrice.Value);
                }

                if (request.MinYear.HasValue)
                {
                    query = query.Where(c => c.Year >= request.MinYear.Value);
                }

                if (request.MaxYear.HasValue)
                {
                    query = query.Where(c => c.Year <= request.MaxYear.Value);
                }

                if (request.MinMileage.HasValue)
                {
                    query = query.Where(c => c.Mileage >= request.MinMileage.Value);
                }

                if (request.MaxMileage.HasValue)
                {
                    query = query.Where(c => c.Mileage <= request.MaxMileage.Value);
                }

                if (request.Colors != null && request.Colors.Any())
                {
                    query = query.Where(c => c.Color != null && request.Colors.Contains(c.Color));
                }

                if (request.NumberOfDoors != null && request.NumberOfDoors.Any())
                {
                    query = query.Where(c => c.NumberOfDoors.HasValue && request.NumberOfDoors.Contains(c.NumberOfDoors.Value));
                }

                if (request.NumberOfSeats != null && request.NumberOfSeats.Any())
                {
                    query = query.Where(c => c.NumberOfSeats.HasValue && request.NumberOfSeats.Contains(c.NumberOfSeats.Value));
                }

                if (request.IsFeatured.HasValue)
                {
                    query = query.Where(c => c.IsFeatured == request.IsFeatured.Value);
                }

                if (request.IsSold.HasValue)
                {
                    query = query.Where(c => c.IsSold == request.IsSold.Value);
                }

                // Apply sorting
                switch (request.SortBy?.ToLower())
                {
                    case "price":
                        query = request.SortDirection?.ToLower() == "asc" ?
                            query.OrderBy(c => c.Price) : query.OrderByDescending(c => c.Price);
                        break;
                    case "year":
                        query = request.SortDirection?.ToLower() == "asc" ?
                            query.OrderBy(c => c.Year) : query.OrderByDescending(c => c.Year);
                        break;
                    case "mileage":
                        query = request.SortDirection?.ToLower() == "asc" ?
                            query.OrderBy(c => c.Mileage) : query.OrderByDescending(c => c.Mileage);
                        break;
                    case "viewcount":
                        query = request.SortDirection?.ToLower() == "asc" ?
                            query.OrderBy(c => c.ViewCount) : query.OrderByDescending(c => c.ViewCount);
                        break;
                    default:
                        query = request.SortDirection?.ToLower() == "asc" ?
                            query.OrderBy(c => c.CreatedOn) : query.OrderByDescending(c => c.CreatedOn);
                        break;
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply pagination
                var cars = await query
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync();

                var carDtos = cars.Select(MapToCarSummaryResponseDto).ToList();

                var paginatedResponse = new PaginatedResponseDto<CarSummaryResponseDto>
                {
                    Data = carDtos,
                    TotalRecords = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
                };

                return ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>.Success(paginatedResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting cars with filters");
                return ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>.Failed("An error occurred while retrieving cars", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>> GetVendorCarsAsync(Guid vendorId, CarFilterRequest request)
        {
            request.VendorId = vendorId;
            return await GetCarsAsync(request);
        }

        public async Task<ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>> GetUserCarsAsync(Guid userId, CarFilterRequest request)
        {
            try
            {
                var query = _context.Cars
                    .Include(c => c.Brand)
                    .Include(c => c.BodyType)
                    .Include(c => c.FuelType)
                    .Include(c => c.Vendor)
                    .Where(c => c.UserId == userId && !c.IsDeleted);

                // Apply the same filters as GetCarsAsync but for specific user
                // ... (similar filtering logic)

                var totalCount = await query.CountAsync();
                var cars = await query
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync();

                var carDtos = cars.Select(MapToCarSummaryResponseDto).ToList();

                var paginatedResponse = new PaginatedResponseDto<CarSummaryResponseDto>
                {
                    Data = carDtos,
                    TotalRecords = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
                };

                return ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>.Success(paginatedResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting cars for user {UserId}", userId);
                return ApiResponse<PaginatedResponseDto<CarSummaryResponseDto>>.Failed("An error occurred while retrieving user cars", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> DeleteCarAsync(Guid carId, Guid userId)
        {
            try
            {
                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                if (car.UserId != userId)
                {
                    return ApiResponse<bool>.Failed("Unauthorized to delete this car", HttpStatusCode.Forbidden);
                }

                car.IsDeleted = true;
                car.DeletedOn = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Car deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting car {CarId}", carId);
                return ApiResponse<bool>.Failed("An error occurred while deleting the car", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> MarkCarAsSoldAsync(Guid carId, Guid userId)
        {
            try
            {
                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                if (car.UserId != userId)
                {
                    return ApiResponse<bool>.Failed("Unauthorized to update this car", HttpStatusCode.Forbidden);
                }

                car.IsSold = true;
                car.SoldDate = DateTime.UtcNow;
                car.Status = CarStatus.Sold;
                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true, "Car marked as sold successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while marking car {CarId} as sold", carId);
                return ApiResponse<bool>.Failed("An error occurred while updating the car", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> UploadCarImagesAsync(Guid carId, List<IFormFile> images, int? primaryImageIndex, Guid userId)
        {
            try
            {
                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                // Check if user has permission to update this car
                if (car.UserId != userId)
                {
                    // Check if user is a vendor and owns this car
                    var vendor = await _context.Vendors.FirstOrDefaultAsync(v => v.UserId == userId && v.Id == car.VendorId);
                    if (vendor == null)
                    {
                        return ApiResponse<bool>.Failed("Unauthorized to update this car", HttpStatusCode.Forbidden);
                    }
                }

                // Validate images
                if (images == null || !images.Any())
                {
                    return ApiResponse<bool>.Failed("At least one image is required", HttpStatusCode.BadRequest);
                }

                // Upload images using the helper method
                var uploadResult = await UploadCarImagesInternalAsync(images, primaryImageIndex, carId);

                if (!string.IsNullOrEmpty(uploadResult.ErrorMessage))
                {
                    return ApiResponse<bool>.Failed(uploadResult.ErrorMessage, HttpStatusCode.BadRequest);
                }

                // Update car with new images
                car.ImageKeys = JsonSerializer.Serialize(uploadResult.ImageKeys);
                car.PrimaryImageKey = uploadResult.PrimaryImageKey;

                // Set car status based on user role
                var isAdmin = await IsUserAdminAsync(userId);
                if (isAdmin)
                {
                    car.Status = CarStatus.Active;
                    WatchLogger.Log($"Car {carId} marked as Active by admin {userId}", "UploadCarImagesAsync");
                }
                else
                {
                    car.Status = CarStatus.PendingApproval;
                    WatchLogger.Log($"Car {carId} marked as PendingApproval by user {userId}", "UploadCarImagesAsync");
                }

                await _context.SaveChangesAsync();

                WatchLogger.Log($"Successfully uploaded {uploadResult.ImageKeys.Count} images for car {carId}", "UploadCarImagesAsync");
                return ApiResponse<bool>.Success(true, "Car images uploaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while uploading images for car {CarId}", carId);
                return ApiResponse<bool>.Failed("An error occurred while uploading car images", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> UpdateCarImagesAsync(Guid carId, List<IFormFile> images, int? primaryImageIndex, Guid userId)
        {
            try
            {
                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                // Check if user has permission to update this car
                if (car.UserId != userId)
                {
                    // Check if user is a vendor and owns this car
                    var vendor = await _context.Vendors.FirstOrDefaultAsync(v => v.UserId == userId && v.Id == car.VendorId);
                    if (vendor == null)
                    {
                        return ApiResponse<bool>.Failed("Unauthorized to update this car", HttpStatusCode.Forbidden);
                    }
                }

                // Get existing image keys to delete them later
                var existingImageKeys = new List<string>();
                if (!string.IsNullOrEmpty(car.ImageKeys))
                {
                    try
                    {
                        existingImageKeys = JsonSerializer.Deserialize<List<string>>(car.ImageKeys) ?? new List<string>();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to deserialize existing image keys for car {CarId}", carId);
                    }
                }

                // Validate new images
                if (images == null || !images.Any())
                {
                    return ApiResponse<bool>.Failed("At least one image is required", HttpStatusCode.BadRequest);
                }

                // Validate primary image index
                if (primaryImageIndex.HasValue && (primaryImageIndex.Value < 0 || primaryImageIndex.Value >= images.Count))
                {
                    return ApiResponse<bool>.Failed("Invalid primary image index", HttpStatusCode.BadRequest);
                }

                // Upload new images to AWS S3
                var uploadedImageKeys = new List<string>();
                string? primaryImageKey = null;

                for (int i = 0; i < images.Count; i++)
                {
                    var image = images[i];

                    // Validate image file
                    if (image.Length > AwsConstants.MaxFileSizes.Images * 1024 * 1024)
                    {
                        return ApiResponse<bool>.Failed($"Image {image.FileName} exceeds maximum size of {AwsConstants.MaxFileSizes.Images}MB", HttpStatusCode.BadRequest);
                    }

                    var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                    if (!allowedTypes.Contains(image.ContentType.ToLower()))
                    {
                        return ApiResponse<bool>.Failed($"Image {image.FileName} has invalid format. Only JPEG, PNG, GIF, and WebP are allowed", HttpStatusCode.BadRequest);
                    }

                    try
                    {
                        var imageKey = await _awsService.UploadFileAsync(image, AwsConstants.Folders.CarImages);
                        uploadedImageKeys.Add(imageKey);

                        // Set primary image key
                        if (primaryImageIndex.HasValue && i == primaryImageIndex.Value)
                        {
                            primaryImageKey = imageKey;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to upload image {FileName} for car {CarId}", image.FileName, carId);

                        // Clean up already uploaded images
                        foreach (var uploadedKey in uploadedImageKeys)
                        {
                            try
                            {
                                await _awsService.DeleteFileAsync(uploadedKey);
                            }
                            catch (Exception deleteEx)
                            {
                                _logger.LogError(deleteEx, "Failed to clean up uploaded image {ImageKey}", uploadedKey);
                            }
                        }

                        return ApiResponse<bool>.Failed($"Failed to upload image {image.FileName}", HttpStatusCode.InternalServerError);
                    }
                }

                // If no primary image index specified, use the first image
                if (!primaryImageIndex.HasValue && uploadedImageKeys.Any())
                {
                    primaryImageKey = uploadedImageKeys.First();
                }

                // Update car with new images
                car.ImageKeys = JsonSerializer.Serialize(uploadedImageKeys);
                car.PrimaryImageKey = primaryImageKey;
                await _context.SaveChangesAsync();

                // Delete old images from S3 (do this after successful database update)
                foreach (var oldImageKey in existingImageKeys)
                {
                    try
                    {
                        await _awsService.DeleteFileAsync(oldImageKey);
                        WatchLogger.Log($"Deleted old image {oldImageKey} for car {carId}", "UpdateCarImagesAsync");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete old image {ImageKey} for car {CarId}", oldImageKey, carId);
                        // Don't fail the entire operation if old image deletion fails
                    }
                }

                WatchLogger.Log($"Successfully updated {uploadedImageKeys.Count} images for car {carId}", "UpdateCarImagesAsync");
                return ApiResponse<bool>.Success(true, "Car images updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating images for car {CarId}", carId);
                return ApiResponse<bool>.Failed("An error occurred while updating car images", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> IncrementViewCountAsync(Guid carId)
        {
            try
            {
                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                car.ViewCount++;
                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while incrementing view count for car {CarId}", carId);
                return ApiResponse<bool>.Failed("An error occurred while updating view count", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> IncrementInquiryCountAsync(Guid carId)
        {
            try
            {
                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                car.InquiryCount++;
                await _context.SaveChangesAsync();

                return ApiResponse<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while incrementing inquiry count for car {CarId}", carId);
                return ApiResponse<bool>.Failed("An error occurred while updating inquiry count", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<List<CarSummaryResponseDto>>> GetFeaturedCarsAsync(int count = 10)
        {
            try
            {
                var cars = await _context.Cars
                    .Include(c => c.Brand)
                    .Include(c => c.BodyType)
                    .Include(c => c.FuelType)
                    .Include(c => c.Vendor)
                    .Where(c => c.IsFeatured && !c.IsDeleted && c.Status == CarStatus.Active)
                    .OrderByDescending(c => c.CreatedOn)
                    .Take(count)
                    .ToListAsync();

                var carDtos = cars.Select(MapToCarSummaryResponseDto).ToList();
                return ApiResponse<List<CarSummaryResponseDto>>.Success(carDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting featured cars");
                return ApiResponse<List<CarSummaryResponseDto>>.Failed("An error occurred while retrieving featured cars", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<List<CarSummaryResponseDto>>> GetRecentCarsAsync(int count = 10)
        {
            try
            {
                var cars = await _context.Cars
                    .Include(c => c.Brand)
                    .Include(c => c.BodyType)
                    .Include(c => c.FuelType)
                    .Include(c => c.Vendor)
                    .Where(c => !c.IsDeleted && c.Status == CarStatus.Active)
                    .OrderByDescending(c => c.CreatedOn)
                    .Take(count)
                    .ToListAsync();

                var carDtos = cars.Select(MapToCarSummaryResponseDto).ToList();
                return ApiResponse<List<CarSummaryResponseDto>>.Success(carDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting recent cars");
                return ApiResponse<List<CarSummaryResponseDto>>.Failed("An error occurred while retrieving recent cars", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<CarStatisticsResponseDto>> GetCarStatisticsAsync(Guid? userId = null, Guid? vendorId = null)
        {
            try
            {
                var query = _context.Cars.Where(c => !c.IsDeleted);

                if (userId.HasValue)
                {
                    query = query.Where(c => c.UserId == userId.Value);
                }

                if (vendorId.HasValue)
                {
                    query = query.Where(c => c.VendorId == vendorId.Value);
                }

                var cars = await query
                    .Include(c => c.Brand)
                    .ToListAsync();

                var statistics = new CarStatisticsResponseDto
                {
                    TotalCars = cars.Count,
                    ActiveCars = cars.Count(c => c.Status == CarStatus.Active),
                    SoldCars = cars.Count(c => c.IsSold),
                    DraftCars = cars.Count(c => c.Status == CarStatus.Draft),
                    FeaturedCars = cars.Count(c => c.IsFeatured),
                    AveragePrice = cars.Any() ? cars.Average(c => c.Price) : 0,
                    TotalViews = cars.Sum(c => c.ViewCount),
                    TotalInquiries = cars.Sum(c => c.InquiryCount),
                    CarsByBrand = cars.GroupBy(c => c.Brand.Name).ToDictionary(g => g.Key, g => g.Count()),
                    CarsByCondition = cars.GroupBy(c => c.Condition.ToString()).ToDictionary(g => g.Key, g => g.Count()),
                    CarsByYear = cars.GroupBy(c => c.Year).ToDictionary(g => g.Key, g => g.Count())
                };

                return ApiResponse<CarStatisticsResponseDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting car statistics");
                return ApiResponse<CarStatisticsResponseDto>.Failed("An error occurred while retrieving car statistics", HttpStatusCode.InternalServerError);
            }
        }

        private CarSummaryResponseDto MapToCarSummaryResponseDto(Data.Entities.Car car)
        {
            return new CarSummaryResponseDto
            {
                Id = car.Id,
                Model = car.Model,
                Year = car.Year,
                Price = car.Price,
                Mileage = car.Mileage,
                Color = car.Color,
                Condition = car.Condition,
                Status = car.Status,
                PrimaryImageUrl = !string.IsNullOrEmpty(car.PrimaryImageKey) ? _awsService.GetPresignedUrlAsync(car.PrimaryImageKey).Result : null,
                IsFeatured = car.IsFeatured,
                IsSold = car.IsSold,
                ViewCount = car.ViewCount,
                CreatedOn = car.CreatedOn,
                BrandName = car.Brand.Name,
                BodyTypeName = car.BodyType.Name,
                FuelTypeName = car.FuelType.Name,
                VendorName = car.Vendor?.BusinessName
            };
        }

        public async Task<ApiResponse<bool>> ApproveCarAsync(Guid carId, ApproveCarRequest request, Guid adminUserId)
        {
            try
            {
                // Verify admin privileges
                var isAdmin = await IsUserAdminAsync(adminUserId);
                if (!isAdmin)
                {
                    return ApiResponse<bool>.Failed("Unauthorized. Only admin users can approve cars", HttpStatusCode.Forbidden);
                }

                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                // Check if car is in a state that can be approved
                if (car.Status != CarStatus.PendingApproval)
                {
                    return ApiResponse<bool>.Failed($"Car cannot be approved. Current status: {car.Status}", HttpStatusCode.BadRequest);
                }

                // Update car status and details
                car.Status = CarStatus.Active;
                car.IsFeatured = request.MarkAsFeatured;
                car.ModifiedOn = DateTime.UtcNow;

                // Add approval notes if provided
                if (!string.IsNullOrEmpty(request.ApprovalNotes))
                {
                    // You might want to add an ApprovalNotes field to the Car entity
                    // For now, we'll log it
                    WatchLogger.Log($"Car {carId} approved by admin {adminUserId} with notes: {request.ApprovalNotes}", "ApproveCarAsync");
                }

                await _context.SaveChangesAsync();

                WatchLogger.Log($"Car {carId} approved by admin {adminUserId}. Featured: {request.MarkAsFeatured}", "ApproveCarAsync");
                return ApiResponse<bool>.Success(true, "Car approved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while approving car {CarId} by admin {AdminUserId}", carId, adminUserId);
                return ApiResponse<bool>.Failed("An error occurred while approving the car", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<bool>> RejectCarAsync(Guid carId, RejectCarRequest request, Guid adminUserId)
        {
            try
            {
                // Verify admin privileges
                var isAdmin = await IsUserAdminAsync(adminUserId);
                if (!isAdmin)
                {
                    return ApiResponse<bool>.Failed("Unauthorized. Only admin users can reject cars", HttpStatusCode.Forbidden);
                }

                var car = await _context.Cars.FindAsync(carId);
                if (car == null || car.IsDeleted)
                {
                    return ApiResponse<bool>.Failed("Car not found", HttpStatusCode.NotFound);
                }

                // Check if car is in a state that can be rejected
                if (car.Status != CarStatus.PendingApproval)
                {
                    return ApiResponse<bool>.Failed($"Car cannot be rejected. Current status: {car.Status}", HttpStatusCode.BadRequest);
                }

                // Update car status
                car.Status = CarStatus.Rejected;
                car.ModifiedOn = DateTime.UtcNow;

                // Log rejection details
                WatchLogger.Log($"Car {carId} rejected by admin {adminUserId}. Reason: {request.RejectionReason}", "RejectCarAsync");
                if (!string.IsNullOrEmpty(request.AdditionalNotes))
                {
                    WatchLogger.Log($"Additional rejection notes for car {carId}: {request.AdditionalNotes}", "RejectCarAsync");
                }

                await _context.SaveChangesAsync();

                // TODO: Send notification to car owner about rejection
                // You might want to implement email notification here

                return ApiResponse<bool>.Success(true, "Car rejected successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while rejecting car {CarId} by admin {AdminUserId}", carId, adminUserId);
                return ApiResponse<bool>.Failed("An error occurred while rejecting the car", HttpStatusCode.InternalServerError);
            }
        }

        /// <summary>
        /// Uploads car images to AWS S3 and returns the uploaded image keys
        /// </summary>
        /// <param name="images">The image files to upload</param>
        /// <param name="primaryImageIndex">Index of the primary image (0-based)</param>
        /// <param name="carId">The car ID for logging purposes</param>
        /// <returns>Tuple containing list of uploaded image keys and primary image key</returns>
        private async Task<(List<string> ImageKeys, string? PrimaryImageKey, string? ErrorMessage)> UploadCarImagesInternalAsync(
            List<IFormFile> images, int? primaryImageIndex, Guid carId)
        {
            var uploadedImageKeys = new List<string>();
            string? primaryImageKey = null;

            // Validate primary image index
            if (primaryImageIndex.HasValue && (primaryImageIndex.Value < 0 || primaryImageIndex.Value >= images.Count))
            {
                return (new List<string>(), null, "Invalid primary image index");
            }

            for (int i = 0; i < images.Count; i++)
            {
                var image = images[i];

                // Validate image file
                if (image.Length > AwsConstants.MaxFileSizes.Images * 1024 * 1024)
                {
                    return (new List<string>(), null, $"Image {image.FileName} exceeds maximum size of {AwsConstants.MaxFileSizes.Images}MB");
                }

                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                if (!allowedTypes.Contains(image.ContentType.ToLower()))
                {
                    return (new List<string>(), null, $"Image {image.FileName} has invalid format. Only JPEG, PNG, GIF, and WebP are allowed");
                }

                try
                {
                    var imageKey = await _awsService.UploadFileAsync(image, AwsConstants.Folders.CarImages);
                    uploadedImageKeys.Add(imageKey);

                    // Set primary image key
                    if (primaryImageIndex.HasValue && i == primaryImageIndex.Value)
                    {
                        primaryImageKey = imageKey;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to upload image {FileName} for car {CarId}", image.FileName, carId);

                    // Clean up already uploaded images
                    foreach (var uploadedKey in uploadedImageKeys)
                    {
                        try
                        {
                            await _awsService.DeleteFileAsync(uploadedKey);
                        }
                        catch (Exception deleteEx)
                        {
                            _logger.LogError(deleteEx, "Failed to clean up uploaded image {ImageKey}", uploadedKey);
                        }
                    }

                    return (new List<string>(), null, $"Failed to upload image {image.FileName}");
                }
            }

            // If no primary image index specified, use the first image
            if (!primaryImageIndex.HasValue && uploadedImageKeys.Any())
            {
                primaryImageKey = uploadedImageKeys.First();
            }

            return (uploadedImageKeys, primaryImageKey, null);
        }

        /// <summary>
        /// Checks if the user has admin privileges
        /// </summary>
        /// <param name="userId">The user ID to check</param>
        /// <returns>True if user is admin, false otherwise</returns>
        private async Task<bool> IsUserAdminAsync(Guid userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null) return false;

                var userRoles = await _userManager.GetRolesAsync(user);
                return userRoles.Any(role =>
                    role.Equals("Super Admin", StringComparison.OrdinalIgnoreCase) ||
                    role.Equals("Content Admin", StringComparison.OrdinalIgnoreCase) ||
                    role.Equals("Support Admin", StringComparison.OrdinalIgnoreCase) ||
                    role.Equals("Financial Admin", StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} is admin", userId);
                return false;
            }
        }
    }
}
