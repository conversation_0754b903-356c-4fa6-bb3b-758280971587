# Admin Dashboard API Documentation

## Overview
The Admin Dashboard API provides endpoints to retrieve comprehensive dashboard data for administrators of the Dubai Car Marketplace platform. These endpoints provide real-time statistics, pending vendor applications, support ticket summaries, and revenue analytics.

## Authentication
All dashboard endpoints require admin authentication with a valid JWT token.

## Endpoints

### 1. Get Complete Dashboard Data
**Endpoint:** `GET /api/admin/dashboard`

**Description:** Retrieves all dashboard data in a single request including statistics, pending vendor applications, support tickets, and revenue data.

**Response:**
```json
{
  "isSuccess": true,
  "statusCode": 200,
  "message": "Dashboard data retrieved successfully",
  "data": {
    "stats": {
      "totalUsers": 12543,
      "totalUsersChangePercentage": 8.5,
      "activeVendors": 248,
      "pendingVendors": 15,
      "totalListings": 3847,
      "totalListingsChangePercentage": 12.5,
      "platformRevenue": 45230.00,
      "platformRevenueChangePercentage": 17.4,
      "lastMonthRevenue": 38540.00
    },
    "pendingVendorApplications": [
      {
        "id": 123,
        "businessName": "Premium Motors LLC",
        "licenseNumber": "123456",
        "businessEmail": "<EMAIL>",
        "submissionDate": "2025-07-10T10:30:00Z",
        "status": "Pending",
        "daysSinceSubmission": 3
      }
    ],
    "supportTickets": {
      "openTickets": 12,
      "inProgressTickets": 8,
      "resolvedToday": 15,
      "recentTickets": []
    },
    "revenue": {
      "currentMonthRevenue": 45230.00,
      "lastMonthRevenue": 38540.00,
      "growthPercentage": 17.4,
      "monthlyRevenue": [
        {
          "month": "Jan",
          "year": 2025,
          "revenue": 32000.00
        }
      ],
      "revenueBySource": [
        {
          "source": "Subscription Fees",
          "amount": 25000.00,
          "percentage": 55.3
        }
      ]
    }
  }
}
```

### 2. Get Dashboard Statistics
**Endpoint:** `GET /api/admin/dashboard/stats`

**Description:** Retrieves only the key statistics for the dashboard cards.

**Response:**
```json
{
  "isSuccess": true,
  "statusCode": 200,
  "message": "Dashboard statistics retrieved successfully",
  "data": {
    "totalUsers": 12543,
    "totalUsersChangePercentage": 8.5,
    "activeVendors": 248,
    "pendingVendors": 15,
    "totalListings": 3847,
    "totalListingsChangePercentage": 12.5,
    "platformRevenue": 45230.00,
    "platformRevenueChangePercentage": 17.4,
    "lastMonthRevenue": 38540.00
  }
}
```

### 3. Get Pending Vendor Applications
**Endpoint:** `GET /api/admin/dashboard/pending-vendor-applications`

**Query Parameters:**
- `count` (optional): Number of applications to return (1-50, default: 5)

**Description:** Retrieves the most recent pending vendor applications for dashboard display.

**Response:**
```json
{
  "isSuccess": true,
  "statusCode": 200,
  "message": "Pending vendor applications retrieved successfully",
  "data": [
    {
      "id": 123,
      "businessName": "Premium Motors LLC",
      "licenseNumber": "123456",
      "businessEmail": "<EMAIL>",
      "submissionDate": "2025-07-10T10:30:00Z",
      "status": "Pending",
      "daysSinceSubmission": 3
    }
  ]
}
```

### 4. Get Platform Revenue Data
**Endpoint:** `GET /api/admin/dashboard/revenue`

**Description:** Retrieves comprehensive revenue analytics including monthly trends and source breakdown.

**Response:**
```json
{
  "isSuccess": true,
  "statusCode": 200,
  "message": "Platform revenue data retrieved successfully",
  "data": {
    "currentMonthRevenue": 45230.00,
    "lastMonthRevenue": 38540.00,
    "growthPercentage": 17.4,
    "monthlyRevenue": [
      {
        "month": "Jan",
        "year": 2025,
        "revenue": 32000.00
      },
      {
        "month": "Feb",
        "year": 2025,
        "revenue": 34500.00
      }
    ],
    "revenueBySource": [
      {
        "source": "Subscription Fees",
        "amount": 25000.00,
        "percentage": 55.3
      },
      {
        "source": "Commission",
        "amount": 15000.00,
        "percentage": 33.2
      },
      {
        "source": "Advertising",
        "amount": 5230.00,
        "percentage": 11.5
      }
    ]
  }
}
```

## Error Responses

### 400 Bad Request
```json
{
  "isSuccess": false,
  "statusCode": 400,
  "message": "Count must be between 1 and 50.",
  "data": null
}
```

### 401 Unauthorized
```json
{
  "isSuccess": false,
  "statusCode": 401,
  "message": "Unauthorized access",
  "data": null
}
```

### 403 Forbidden
```json
{
  "isSuccess": false,
  "statusCode": 403,
  "message": "Access denied. Admin privileges required.",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "isSuccess": false,
  "statusCode": 500,
  "message": "An error occurred while retrieving dashboard data",
  "data": null
}
```

## Implementation Notes

### Current Data Sources
- **User Statistics**: Retrieved from the `Users` table
- **Vendor Statistics**: Retrieved from the `VendorApplications` table
- **Listings Statistics**: Placeholder data (requires `Listings` entity implementation)
- **Revenue Data**: Placeholder data (requires `Transactions`/`Payments` entity implementation)
- **Support Tickets**: Placeholder data (requires `SupportTickets` entity implementation)

### Future Enhancements
1. **Real Revenue Tracking**: Implement when payment/transaction entities are added
2. **Listings Integration**: Connect to actual listings data when available
3. **Support Ticket System**: Integrate with support ticket management system
4. **Caching**: Add Redis caching for frequently accessed dashboard data
5. **Real-time Updates**: Consider SignalR for live dashboard updates

### Performance Considerations
- Dashboard data is calculated on each request
- Consider implementing caching for expensive operations
- Monthly revenue calculations may need optimization for large datasets
- Use appropriate database indexing for date-based queries

## Usage Examples

### Frontend Integration
```javascript
// Get complete dashboard data
const dashboardData = await fetch('/api/admin/dashboard', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// Get only statistics for card updates
const stats = await fetch('/api/admin/dashboard/stats', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### Dashboard Card Components
The API responses are structured to directly support the dashboard UI:
- **Stats Cards**: Use `data.stats` for the four main statistic cards
- **Vendor Applications Table**: Use `data.pendingVendorApplications`
- **Support Tickets**: Use `data.supportTickets`
- **Revenue Charts**: Use `data.revenue.monthlyRevenue` for trend charts

## Security
- All endpoints require admin authentication
- Input validation on query parameters
- Proper error handling to prevent information disclosure
- Audit logging for all dashboard data access
