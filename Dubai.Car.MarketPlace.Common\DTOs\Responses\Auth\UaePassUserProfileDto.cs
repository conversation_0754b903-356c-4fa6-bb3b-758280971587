using System.Text.Json.Serialization;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth
{
    /// <summary>
    /// Response DTO for UAE PASS user profile information
    /// </summary>
    public class UaePassUserProfileDto
    {
        /// <summary>
        /// Unique user identifier from UAE PASS
        /// </summary>
        [JsonPropertyName("sub")]
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// User's first name
        /// </summary>
        [JsonPropertyName("firstnameEN")]
        public string FirstNameEn { get; set; } = string.Empty;

        /// <summary>
        /// User's first name in Arabic
        /// </summary>
        [JsonPropertyName("firstnameAR")]
        public string? FirstNameAr { get; set; }

        /// <summary>
        /// User's last name
        /// </summary>
        [JsonPropertyName("lastnameEN")]
        public string LastNameEn { get; set; } = string.Empty;

        /// <summary>
        /// User's last name in Arabic
        /// </summary>
        [JsonPropertyName("lastnameAR")]
        public string? LastNameAr { get; set; }

        /// <summary>
        /// User's full name
        /// </summary>
        [JsonPropertyName("fullnameEN")]
        public string? FullNameEn { get; set; }

        /// <summary>
        /// User's full name in Arabic
        /// </summary>
        [JsonPropertyName("fullnameAR")]
        public string? FullNameAr { get; set; }

        /// <summary>
        /// User's email address
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// User's mobile number
        /// </summary>
        [JsonPropertyName("mobile")]
        public string? Mobile { get; set; }

        /// <summary>
        /// User's Emirates ID
        /// </summary>
        [JsonPropertyName("idn")]
        public string? EmiratesId { get; set; }

        /// <summary>
        /// User's UUID from UAE PASS
        /// </summary>
        [JsonPropertyName("uuid")]
        public string? Uuid { get; set; }

        /// <summary>
        /// User's gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// User's date of birth
        /// </summary>
        [JsonPropertyName("dob")]
        public string? DateOfBirth { get; set; }

        /// <summary>
        /// User's nationality
        /// </summary>
        [JsonPropertyName("nationalityEN")]
        public string? NationalityEn { get; set; }

        /// <summary>
        /// User's nationality in Arabic
        /// </summary>
        [JsonPropertyName("nationalityAR")]
        public string? NationalityAr { get; set; }

        /// <summary>
        /// User's passport number
        /// </summary>
        [JsonPropertyName("passportNumber")]
        public string? PassportNumber { get; set; }

        /// <summary>
        /// User's address
        /// </summary>
        [JsonPropertyName("address")]
        public string? Address { get; set; }

        /// <summary>
        /// User's photo URL
        /// </summary>
        [JsonPropertyName("photo")]
        public string? Photo { get; set; }

        /// <summary>
        /// Indicates if the user is verified
        /// </summary>
        [JsonPropertyName("userType")]
        public string? UserType { get; set; }
    }
}
