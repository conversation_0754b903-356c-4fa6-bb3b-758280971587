using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    public static class RoleSeeder
    {
        public static async Task SeedRolesAsync(CarContext context, ILogger logger)
        {
            try
            {
                // Check if roles already exist
                if (await context.Roles.AnyAsync())
                {
                    logger.LogInformation("Roles already exist. Skipping role seeding.");
                    return;
                }

                var roles = new List<Role>
                {
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Super Admin",
                        NormalizedName = "SUPER ADMIN",
                        Description = "Full system access and control",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Content Admin",
                        NormalizedName = "CONTENT ADMIN",
                        Description = "Manage content, blogs, and moderation",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Support Admin",
                        NormalizedName = "SUPPORT ADMIN",
                        Description = "Handle customer support and tickets",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Financial Admin",
                        NormalizedName = "FINANCIAL ADMIN",
                        Description = "Manage payments and subscriptions",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Customer",
                        NormalizedName = "CUSTOMER",
                        Description = "Regular customers who can browse and purchase",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Private Seller",
                        NormalizedName = "PRIVATE SELLER",
                        Description = "Private individuals who can sell cars",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Role
                    {
                        Id = Guid.NewGuid(),
                        Name = "Vendor",
                        NormalizedName = "VENDOR",
                        Description = "Approved vendors who can sell cars",
                        IsEditable = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.Roles.AddRange(roles);
                await context.SaveChangesAsync();

                logger.LogInformation($"Successfully seeded {roles.Count} roles.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding roles.");
                throw;
            }
        }
    }
}
