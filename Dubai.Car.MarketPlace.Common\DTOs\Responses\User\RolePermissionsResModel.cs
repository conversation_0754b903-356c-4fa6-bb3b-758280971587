

using Dubai.Car.MarketPlace.Common.Enums;
using static Dubai.Car.MarketPlace.Common.Enums.AuthEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.User;

public class RolePermissionsResModel
{
    public long RoleId { get; set; }
    public required string RoleName { get; set; }
    public long? UserRoleId { get; set; }
    public List<Permissions> UserRolePermissions { get; set; } = new List<Permissions>();
} 