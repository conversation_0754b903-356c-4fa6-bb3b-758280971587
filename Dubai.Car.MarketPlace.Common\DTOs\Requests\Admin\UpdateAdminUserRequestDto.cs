using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin
{
    /// <summary>
    /// Request DTO for updating an admin user
    /// </summary>
    public class UpdateAdminUserRequestDto
    {
        /// <summary>
        /// First name of the admin user
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = default!;

        /// <summary>
        /// Last name of the admin user
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = default!;

        /// <summary>
        /// Phone number of the admin user
        /// </summary>
        [Phone]
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Role ID to assign to the admin user
        /// </summary>
        [Required]
        public Guid RoleId { get; set; }

        /// <summary>
        /// Gender of the admin user
        /// </summary>
        [StringLength(10)]
        public string? Gender { get; set; }

        /// <summary>
        /// Country of the admin user
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }

        /// <summary>
        /// State of the admin user
        /// </summary>
        [StringLength(50)]
        public string? State { get; set; }

        /// <summary>
        /// Whether the admin user is active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
