using System.ComponentModel;
using System.Reflection;

namespace Dubai.Car.MarketPlace.Common.Utility
{
    public static class EnumHelper
    {
        /// <summary>
        /// Gets the description attribute value from an enum member
        /// </summary>
        /// <param name="enumValue">The enum value to get the description from</param>
        /// <returns>The description attribute value or the enum name if no description is found</returns>
        public static string GetDescription(this Enum enumValue)
        {
            Type type = enumValue.GetType();
            string? name = Enum.GetName(type, enumValue);
            
            if (name != null)
            {
                FieldInfo? field = type.GetField(name);
                if (field != null)
                {
                    DescriptionAttribute? attribute = (DescriptionAttribute?)field.GetCustomAttribute(typeof(DescriptionAttribute));
                    if (attribute != null)
                    {
                        return attribute.Description;
                    }
                }
            }
            
            return enumValue.ToString();
        }
    }
}
