﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Dubai.Car.MarketPlace.Data.Entities
{
    [Index(nameof(Name), IsUnique = true)]
    public class Role : IdentityRole<Guid>
    {
        public string? Description { get; set; }
        public bool IsEditable { get; set; } = true;
        public Guid? CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }
}
