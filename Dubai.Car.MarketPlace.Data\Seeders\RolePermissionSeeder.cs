using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Database;
using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Dubai.Car.MarketPlace.Data.Seeders
{
    public static class RolePermissionSeeder
    {
        public static async Task SeedRolePermissionsAsync(CarContext context, ILogger logger)
        {
            try
            {
                // Check if role permissions already exist
                if (await context.RolePermissions.AnyAsync())
                {
                    logger.LogInformation("Role permissions already exist. Skipping role permission seeding.");
                    return;
                }

                // Get all roles and permissions
                var roles = await context.Roles.ToListAsync();
                var permissions = await context.Permissions.ToListAsync();

                if (!roles.Any() || !permissions.Any())
                {
                    logger.LogWarning("Roles or permissions not found. Please ensure they are seeded first.");
                    return;
                }

                var rolePermissions = new List<RolePermission>();

                // Super Admin - All permissions
                var superAdminRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.SuperAdmin.GetDescription());
                if (superAdminRole != null)
                {
                    foreach (var permission in permissions)
                    {
                        rolePermissions.Add(new RolePermission
                        {
                            Id = Guid.NewGuid(),
                            RoleId = superAdminRole.Id,
                            PermissionId = permission.Id,
                            CreatedOn = DateTime.UtcNow,
                            IsDeleted = false
                        });
                    }
                }

                // Content Admin - Content management permissions
                var contentAdminRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.ContentAdmin.GetDescription());
                if (contentAdminRole != null)
                {
                    var contentPermissions = new[]
                    {
                        RoleEnums.SystemPermissions.ContentManagement,
                        RoleEnums.SystemPermissions.BlogManagement,
                        RoleEnums.SystemPermissions.ReviewModeration,
                        RoleEnums.SystemPermissions.AdvertisementManagement,
                        RoleEnums.SystemPermissions.ReportGenerate
                    };

                    foreach (var permissionEnum in contentPermissions)
                    {
                        var permissionName = permissionEnum.ToString();
                        var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                        if (permission != null)
                        {
                            rolePermissions.Add(new RolePermission
                            {
                                Id = Guid.NewGuid(),
                                RoleId = contentAdminRole.Id,
                                PermissionId = permission.Id,
                                CreatedOn = DateTime.UtcNow,
                                IsDeleted = false
                            });
                        }
                    }
                }

                // Support Admin - Support and basic user management permissions
                var supportAdminRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.SupportAdmin.GetDescription());
                if (supportAdminRole != null)
                {
                    var supportPermissions = new[]
                    {
                        RoleEnums.SystemPermissions.SupportTickets,
                        RoleEnums.SystemPermissions.UserCommunication,
                        RoleEnums.SystemPermissions.BasicUserManagement,
                        RoleEnums.SystemPermissions.UserManagement
                    };

                    foreach (var permissionEnum in supportPermissions)
                    {
                        var permissionName = permissionEnum.ToString();
                        var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                        if (permission != null)
                        {
                            rolePermissions.Add(new RolePermission
                            {
                                Id = Guid.NewGuid(),
                                RoleId = supportAdminRole.Id,
                                PermissionId = permission.Id,
                                CreatedOn = DateTime.UtcNow,
                                IsDeleted = false
                            });
                        }
                    }
                }

                // Financial Admin - Financial management permissions
                var financialAdminRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.FinancialAdmin.GetDescription());
                if (financialAdminRole != null)
                {
                    var financialPermissions = new[]
                    {
                        RoleEnums.SystemPermissions.FinancialManagement,
                        RoleEnums.SystemPermissions.PaymentManagement,
                        RoleEnums.SystemPermissions.SubscriptionOversight,
                        RoleEnums.SystemPermissions.FinancialReports,
                        RoleEnums.SystemPermissions.ReportGenerate
                    };

                    foreach (var permissionEnum in financialPermissions)
                    {
                        var permissionName = permissionEnum.ToString();
                        var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                        if (permission != null)
                        {
                            rolePermissions.Add(new RolePermission
                            {
                                Id = Guid.NewGuid(),
                                RoleId = financialAdminRole.Id,
                                PermissionId = permission.Id,
                                CreatedOn = DateTime.UtcNow,
                                IsDeleted = false
                            });
                        }
                    }
                }

                // Customer - Basic permissions
                var customerRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.Customer.GetDescription());
                if (customerRole != null)
                {
                    // Customers typically have minimal permissions - we'll assign basic ones if needed
                    // For now, no specific permissions assigned to customers
                }

                // Private Seller - Basic selling permissions
                var privateSellerRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.PrivateSeller.GetDescription());
                if (privateSellerRole != null)
                {
                    // Private sellers may have limited permissions
                    // For now, no specific permissions assigned
                }

                // Vendor - Enhanced permissions
                var vendorRole = roles.FirstOrDefault(r => r.Name == RoleEnums.SystemRoles.Vendor.GetDescription());
                if (vendorRole != null)
                {
                    var vendorPermissions = new[]
                    {
                        RoleEnums.SystemPermissions.VendorManagement,
                        RoleEnums.SystemPermissions.AdvertisementManagement
                    };

                    foreach (var permissionEnum in vendorPermissions)
                    {
                        var permissionName = permissionEnum.ToString();
                        var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                        if (permission != null)
                        {
                            rolePermissions.Add(new RolePermission
                            {
                                Id = Guid.NewGuid(),
                                RoleId = vendorRole.Id,
                                PermissionId = permission.Id,
                                CreatedOn = DateTime.UtcNow,
                                IsDeleted = false
                            });
                        }
                    }
                }

                context.RolePermissions.AddRange(rolePermissions);
                await context.SaveChangesAsync();

                logger.LogInformation($"Successfully seeded {rolePermissions.Count} role permissions.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding role permissions.");
                throw;
            }
        }
    }
}
