﻿using Dubai.Car.MarketPlace.Common.Enums;
using Dubai.Car.MarketPlace.Data.Entities;
using System.Security.Claims;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    public interface IJwtService
    {
        Task<string> GenerateJwtToken(User user);
        Task<string> GenerateRefreshToken();
        Task<ClaimsPrincipal> GetPrincipalFromExpiredToken(string token);
        Task<bool> BlacklistToken(string token);
        Task<bool> IsTokenBlacklisted(string token);
        Task<List<RoleEnums.SystemPermissions>> GetUserPermissionsEnumAsync(User user);
    }
}
