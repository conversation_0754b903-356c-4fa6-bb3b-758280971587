﻿namespace Dubai.Car.MarketPlace.Common.Constants
{
    /// <summary>
    /// Constants related to AWS S3 storage
    /// </summary>
    public static class AwsConstants
    {
        /// <summary>
        /// Folder paths for different types of uploads
        /// </summary>
        public static class Folders
        {
            /// <summary>
            /// Folder for event banner images
            /// </summary>
            public const string EventBanners = "event-banners";

            /// <summary>
            /// Folder for user profile images
            /// </summary>
            public const string UserProfiles = "user-profiles";

            /// <summary>
            /// Folder for document uploads
            /// </summary>
            public const string Documents = "documents";

            /// <summary>
            /// Folder for car images
            /// </summary>
            public const string CarImages = "car-images";

            /// <summary>
            /// Folder for temporary uploads
            /// </summary>
            public const string Temp = "temp";
        }

        /// <summary>
        /// Maximum file sizes for different types of uploads (in MB)
        /// </summary>
        public static class MaxFileSizes
        {
            /// <summary>
            /// Maximum file size for images (in MB)
            /// </summary>
            public const int Images = 5;

            /// <summary>
            /// Maximum file size for documents (in MB)
            /// </summary>
            public const int Documents = 10;

            /// <summary>
            /// Maximum file size for videos (in MB)
            /// </summary>
            public const int Videos = 1024; // 1GB - increased to support large video files with multipart upload
        }

        /// <summary>
        /// Upload configuration constants
        /// </summary>
        public static class Upload
        {
            /// <summary>
            /// File size threshold for multipart upload (in MB)
            /// Files larger than this will use multipart upload
            /// </summary>
            public const int MultipartThresholdMB = 50;

            /// <summary>
            /// Part size for multipart uploads (in MB)
            /// Each part will be this size (minimum 5MB for AWS S3)
            /// </summary>
            public const int MultipartPartSizeMB = 10;

            /// <summary>
            /// Maximum number of concurrent upload parts
            /// </summary>
            public const int MaxConcurrentParts = 5;

            /// <summary>
            /// Threshold for chunked upload to Twitch (in MB)
            /// Files larger than this will be uploaded in chunks
            /// </summary>
            public const int ChunkedUploadThresholdMB = 100;

            /// <summary>
            /// Chunk size for Twitch uploads (in MB)
            /// </summary>
            public const int ChunkSizeMB = 50;

            /// <summary>
            /// Maximum number of concurrent chunks for Twitch upload
            /// </summary>
            public const int MaxConcurrentChunks = 3;
        }
    }
}
