using Dubai.Car.MarketPlace.Common.DTOs.Requests.Vendor;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor;
using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Interface for vendor-related operations
    /// </summary>
    public interface IVendorService
    {
        /// <summary>
        /// Submit a new vendor application
        /// </summary>
        /// <param name="model">The vendor application details</param>
        /// <returns>Result of the application submission</returns>
        Task<ApiResponse<VendorApplicationResponseDto>> SubmitApplicationAsync(VendorApplicationRequestDto model);
        
        /// <summary>
        /// Get a vendor application by ID
        /// </summary>
        /// <param name="applicationId">The ID of the application</param>
        /// <returns>The vendor application details</returns>
        Task<ApiResponse<VendorApplicationResponseDto>> GetApplicationByIdAsync(Guid applicationId);
        
        /// <summary>
        /// Get all vendor applications with optional filtering and pagination
        /// </summary>
        /// <param name="status">Filter by application status</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated list of vendor applications</returns>
        Task<ApiResponse<PaginatedResponseDto<VendorApplicationResponseDto>>> GetApplicationsAsync(
            VendorApplicationStatus? status = null, 
            int pageNumber = 1, 
            int pageSize = 10);
        
        /// <summary>
        /// Review a vendor application
        /// </summary>
        /// <param name="adminUserId">The ID of the admin reviewing the application</param>
        /// <param name="model">The review details</param>
        /// <returns>Result of the application review</returns>
        Task<ApiResponse<VendorApplicationResponseDto>> ReviewApplicationAsync(Guid adminUserId, VendorApplicationReviewDto model);
        
        /// <summary>
        /// Get a vendor's own application
        /// </summary>
        /// <param name="userId">The ID of the vendor</param>
        /// <returns>The vendor's application details</returns>
        Task<ApiResponse<VendorApplicationResponseDto>> GetVendorOwnApplicationAsync(Guid userId);
    }
}
