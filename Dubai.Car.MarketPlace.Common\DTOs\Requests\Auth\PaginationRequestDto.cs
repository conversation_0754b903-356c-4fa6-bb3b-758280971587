﻿namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth
{
    public class PaginationRequestDto
    {
        private int _pageNumber = 1;
        private int _pageSize = 10;
        
        public int PageNumber
        {
            get => _pageNumber;
            set => _pageNumber = value < 1 ? 1 : value;
        }
        
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = value < 1 ? 10 : value > 50 ? 50 : value;
        }
        
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool IsAscending { get; set; } = true;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Country { get; set; }
        public string? State { get; set; }
    }
}
