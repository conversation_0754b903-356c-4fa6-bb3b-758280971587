using Dubai.Car.MarketPlace.Common.Configurations;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Web;
using WatchDog;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    /// <summary>
    /// Service for handling UAE PASS authentication
    /// </summary>
    public class UaePassService : IUaePassService
    {
        private readonly UaePassSettings _uaePassSettings;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<UaePassService> _logger;
        private readonly HashSet<string> _validStates;

        public UaePassService(
            IOptions<UaePassSettings> uaePassSettings,
            IHttpClientFactory httpClientFactory,
            ILogger<UaePassService> logger)
        {
            _uaePassSettings = uaePassSettings.Value;
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _validStates = new HashSet<string>();
        }

        #region Generate Authorization URL
        public async Task<ApiResponse<UaePassAuthUrlResponseDto>> GenerateAuthorizationUrlAsync(string? state = null)
        {
            try
            {
                // Generate state if not provided
                if (string.IsNullOrEmpty(state))
                {
                    state = GenerateState();
                }

                // Store state for validation
                _validStates.Add(state);

                var queryParams = new Dictionary<string, string>
                {
                    ["response_type"] = "code",
                    ["client_id"] = _uaePassSettings.ClientId,
                    ["redirect_uri"] = _uaePassSettings.RedirectUri,
                    ["scope"] = _uaePassSettings.Scope,
                    ["state"] = state
                };

                var queryString = string.Join("&", queryParams.Select(kvp =>
                    $"{HttpUtility.UrlEncode(kvp.Key)}={HttpUtility.UrlEncode(kvp.Value)}"));

                var authorizationUrl = $"{_uaePassSettings.AuthorizationEndpoint}?{queryString}";

                var response = new UaePassAuthUrlResponseDto
                {
                    AuthorizationUrl = authorizationUrl,
                    State = state
                };

                WatchLogger.Log($"Generated UAE PASS authorization URL with state: {state}", "GenerateAuthorizationUrlAsync");
                return ApiResponse<UaePassAuthUrlResponseDto>.Success(response, "Authorization URL generated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating UAE PASS authorization URL");
                WatchLogger.LogError($"Error generating UAE PASS authorization URL: {ex.Message}", "GenerateAuthorizationUrlAsync");
                return ApiResponse<UaePassAuthUrlResponseDto>.Failed("Failed to generate authorization URL", HttpStatusCode.InternalServerError);
            }
        }
        #endregion

        #region Exchange Code for Token
        public async Task<ApiResponse<UaePassTokenResponseDto>> ExchangeCodeForTokenAsync(string code, string? state = null)
        {
            try
            {
                // Validate state if provided
                if (!string.IsNullOrEmpty(state) && !ValidateState(state))
                {
                    WatchLogger.LogWarning($"Invalid state parameter: {state}", "ExchangeCodeForTokenAsync");
                    return ApiResponse<UaePassTokenResponseDto>.Failed("Invalid state parameter", HttpStatusCode.BadRequest);
                }

                var httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromSeconds(_uaePassSettings.TimeoutSeconds);

                var tokenRequest = new Dictionary<string, string>
                {
                    ["grant_type"] = "authorization_code",
                    ["client_id"] = _uaePassSettings.ClientId,
                    ["client_secret"] = _uaePassSettings.ClientSecret,
                    ["code"] = code,
                    ["redirect_uri"] = _uaePassSettings.RedirectUri
                };

                var content = new FormUrlEncodedContent(tokenRequest);
                content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

                WatchLogger.Log($"Exchanging authorization code for token", "ExchangeCodeForTokenAsync");
                var response = await httpClient.PostAsync(_uaePassSettings.TokenEndpoint, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("UAE PASS token exchange failed. Status: {StatusCode}, Content: {Content}",
                        response.StatusCode, errorContent);
                    WatchLogger.LogError($"UAE PASS token exchange failed. Status: {response.StatusCode}, Content: {errorContent}",
                        "ExchangeCodeForTokenAsync");
                    return ApiResponse<UaePassTokenResponseDto>.Failed("Failed to exchange code for token", HttpStatusCode.BadRequest);
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<UaePassTokenResponseDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
                {
                    WatchLogger.LogError("Invalid token response from UAE PASS", "ExchangeCodeForTokenAsync");
                    return ApiResponse<UaePassTokenResponseDto>.Failed("Invalid token response", HttpStatusCode.BadRequest);
                }

                // Remove state from valid states after successful exchange
                if (!string.IsNullOrEmpty(state))
                {
                    _validStates.Remove(state);
                }

                WatchLogger.Log("Successfully exchanged authorization code for token", "ExchangeCodeForTokenAsync");
                return ApiResponse<UaePassTokenResponseDto>.Success(tokenResponse, "Token exchange successful");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exchanging code for token");
                WatchLogger.LogError($"Error exchanging code for token: {ex.Message}", "ExchangeCodeForTokenAsync");
                return ApiResponse<UaePassTokenResponseDto>.Failed("Failed to exchange code for token", HttpStatusCode.InternalServerError);
            }
        }
        #endregion

        #region Get User Profile
        public async Task<ApiResponse<UaePassUserProfileDto>> GetUserProfileAsync(string accessToken)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromSeconds(_uaePassSettings.TimeoutSeconds);
                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                WatchLogger.Log("Fetching user profile from UAE PASS", "GetUserProfileAsync");
                var response = await httpClient.GetAsync(_uaePassSettings.UserInfoEndpoint);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("UAE PASS user profile fetch failed. Status: {StatusCode}, Content: {Content}",
                        response.StatusCode, errorContent);
                    WatchLogger.LogError($"UAE PASS user profile fetch failed. Status: {response.StatusCode}, Content: {errorContent}",
                        "GetUserProfileAsync");
                    return ApiResponse<UaePassUserProfileDto>.Failed("Failed to fetch user profile", HttpStatusCode.BadRequest);
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                var userProfile = JsonSerializer.Deserialize<UaePassUserProfileDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (userProfile == null)
                {
                    WatchLogger.LogError("Invalid user profile response from UAE PASS", "GetUserProfileAsync");
                    return ApiResponse<UaePassUserProfileDto>.Failed("Invalid user profile response", HttpStatusCode.BadRequest);
                }

                return ApiResponse<UaePassUserProfileDto>.Success(userProfile, "User profile fetched successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user profile from UAE PASS");
                WatchLogger.LogError($"Error fetching user profile from UAE PASS: {ex.Message}", "GetUserProfileAsync");
                return ApiResponse<UaePassUserProfileDto>.Failed("Failed to fetch user profile", HttpStatusCode.InternalServerError);
            }
        }
        #endregion

        #region State Management
        public bool ValidateState(string? state)
        {
            if (string.IsNullOrEmpty(state))
                return false;

            return _validStates.Contains(state);
        }

        public string GenerateState()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }
        #endregion
    }
}
