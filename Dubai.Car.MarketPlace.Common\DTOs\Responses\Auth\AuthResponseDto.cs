﻿using static Dubai.Car.MarketPlace.Common.Enums.AuthEnums;
using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth
{
    public class AuthResponseDto
    {
        public Guid UserId { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool EmailConfirmed { get; set; }
        public string? Token { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? RefreshTokenExpiryTime { get; set; }
        public string Role { get; set; } = string.Empty;
        public string UserType { get; set; } = string.Empty;
        public List<string> Permissions { get; set; } = new List<string>();
    }
}
