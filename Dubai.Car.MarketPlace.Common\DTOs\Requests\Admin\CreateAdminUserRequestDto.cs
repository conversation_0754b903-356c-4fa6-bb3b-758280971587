using System.ComponentModel.DataAnnotations;

namespace Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin
{
    /// <summary>
    /// Request DTO for creating a new admin user
    /// </summary>
    public class CreateAdminUserRequestDto
    {
        /// <summary>
        /// First name of the admin user
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = default!;

        /// <summary>
        /// Last name of the admin user
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = default!;

        /// <summary>
        /// Email address of the admin user
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = default!;

        /// <summary>
        /// Phone number of the admin user
        /// </summary>
        [Phone]
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Role ID to assign to the admin user
        /// </summary>
        [Required]
        public Guid RoleId { get; set; }

        /// <summary>
        /// Temporary password for the admin user
        /// </summary>
        [Required]
        [StringLength(100, MinimumLength = 8)]
        public string TemporaryPassword { get; set; } = default!;

        /// <summary>
        /// Gender of the admin user
        /// </summary>
        [StringLength(10)]
        public string? Gender { get; set; }

        /// <summary>
        /// Country of the admin user
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }

        /// <summary>
        /// State of the admin user
        /// </summary>
        [StringLength(50)]
        public string? State { get; set; }
    }
}
