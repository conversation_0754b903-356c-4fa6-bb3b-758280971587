namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for pending vendor applications in dashboard
    /// </summary>
    public class DashboardVendorApplicationResponseDto
    {
        /// <summary>
        /// Application ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Business name
        /// </summary>
        public string BusinessName { get; set; } = default!;

        /// <summary>
        /// License number
        /// </summary>
        public string LicenseNumber { get; set; } = default!;

        /// <summary>
        /// Business email
        /// </summary>
        public string BusinessEmail { get; set; } = default!;

        /// <summary>
        /// Submission date
        /// </summary>
        public DateTime SubmissionDate { get; set; }

        /// <summary>
        /// Status of the application
        /// </summary>
        public string Status { get; set; } = default!;

        /// <summary>
        /// Days since submission
        /// </summary>
        public int DaysSinceSubmission { get; set; }
    }
}
