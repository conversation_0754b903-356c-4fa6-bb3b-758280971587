﻿using System.Net;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses
{
    public class ApiResponse
    {
        public bool IsSuccess { get; set; }
        public string? ErrorCode { get; set; }
        public HttpStatusCode StatusCode { get; set; }
        public string? Message { get; set; }
        public string? DevMessage { get; set; }
        public string? ExceptionStackTrace { get; set; }


        public ApiResponse()
        {
            StatusCode = HttpStatusCode.BadRequest;
        }

        public static ApiResponse Success(string? message = null)
        {
            return new ApiResponse
            {
                IsSuccess = true,
                StatusCode = HttpStatusCode.OK,
                Message = message ?? "Successful."
            };
        }

        public static ApiResponse Failed(string? message = null, HttpStatusCode httpStatus = HttpStatusCode.BadRequest)
        {
            return new ApiResponse
            {
                IsSuccess = false,
                StatusCode = httpStatus,
                Message = message ?? "Failed."
            };
        }
    }

    public class ApiResponse<TData> : ApiResponse
    {
        public TData? Data { get; set; }

        public ApiResponse()
        {
            StatusCode = HttpStatusCode.BadRequest;
        }

        public static ApiResponse<TData> Success(TData data, string? message = null)
        {
            return new ApiResponse<TData>
            {
                IsSuccess = true,
                StatusCode = HttpStatusCode.OK,
                Message = message ?? "Successful.",
                Data = data
            };
        }

        public static ApiResponse<TData> Failed(string? message = null, HttpStatusCode httpStatus = HttpStatusCode.BadRequest, string? devMessage = null)
        {
            return new ApiResponse<TData>
            {
                IsSuccess = false,
                StatusCode = httpStatus,
                Message = message ?? "Failed.",
                DevMessage = devMessage
            };
        }
    }

}
