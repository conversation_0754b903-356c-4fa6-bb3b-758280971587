using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin
{
    /// <summary>
    /// Response DTO for role information with permissions
    /// </summary>
    public class RoleWithPermissionsResponseDto
    {
        /// <summary>
        /// Role ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Role name
        /// </summary>
        public string Name { get; set; } = default!;

        /// <summary>
        /// Role description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Whether the role is editable
        /// </summary>
        public bool IsEditable { get; set; }

        /// <summary>
        /// Date when the role was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Date when the role was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// List of permissions assigned to this role
        /// </summary>
        public List<SystemPermissions> Permissions { get; set; } = new();
    }
}
